'use client'

import { useState, useEffect, useRef } from 'react'
import { Monitor, MoreVertical, Play, RotateCcw, Eye, Tag, FolderOpen, Terminal, MapPin, User } from 'lucide-react'
import { Client } from '@/types'
import { formatUptime } from '@/lib/utils'
import StatusIndicator from './StatusIndicator'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'

interface ClientTableProps {
  clients: Client[]
  selectedClients: string[]
  onSelectClient: (clientId: string) => void
  onSelectAll: (selected: boolean) => void
  onRemoteConnect: (clientId: string) => void
  onFileManage: (clientId: string) => void
  onCmdExecute: (clientId: string) => void
}

export default function ClientTable({
  clients,
  selectedClients,
  onSelectClient,
  onSelectAll,
  onRemoteConnect,
  onFileManage,
  onCmdExecute
}: ClientTableProps) {
  const [sortField, setSortField] = useState<keyof Client>('name')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)

  // 点击外部关闭下拉菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      const target = event.target as HTMLElement
      if (!target.closest('.dropdown-container')) {
        setOpenDropdown(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleSort = (field: keyof Client) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const sortedClients = [...clients].sort((a, b) => {
    const aValue = a[sortField]
    const bValue = b[sortField]
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue)
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
    }
    
    return 0
  })

  const isAllSelected = clients.length > 0 && selectedClients.length === clients.length

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden hover-lift">
      <div className="overflow-x-auto">
        <table className="w-full divide-y divide-gray-200 table-fixed"
               style={{ minWidth: '1200px' }}>
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left" style={{ width: '50px' }}>
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  onChange={(e) => onSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('status')}
                style={{ width: '80px' }}
              >
                状态
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('name')}
                style={{ width: '200px' }}
              >
                客户端名称
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  style={{ width: '220px' }}>
                归属用户
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('ip')}
                style={{ width: '180px' }}
              >
                IP地址/位置
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  style={{ width: '150px' }}>
                系统信息
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  style={{ width: '160px' }}>
                最后上线/运行时间
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  style={{ width: '120px' }}>
                标签
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  style={{ width: '80px' }}>
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedClients.map((client) => (
              <tr key={client.id} className="hover:bg-gray-50">
                <td className="px-6 py-4" style={{ width: '50px' }}>
                  <input
                    type="checkbox"
                    checked={selectedClients.includes(client.id)}
                    onChange={() => onSelectClient(client.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                <td className="px-4 py-4 whitespace-nowrap" style={{ width: '80px' }}>
                  <StatusIndicator status={client.status} />
                </td>
                <td className="px-6 py-4 whitespace-nowrap" style={{ width: '200px' }}>
                  <div className="flex items-center">
                    <Monitor className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">{client.name}</div>
                      <div className="text-sm text-gray-500">{client.id}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900" style={{ width: '220px' }}>
                  <div className="flex items-center w-full">
                    <User className="h-5 w-5 text-gray-400 mr-3 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <div className="font-medium">{client.owner.name}</div>
                      <div className="text-gray-500 text-xs font-mono overflow-hidden" title={client.owner.uuid}>
                        <span className="block truncate max-w-full">
                          {client.owner.uuid}
                        </span>
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900" style={{ width: '180px' }}>
                  <div>
                    <div className="font-medium">{client.ip}</div>
                    <div className="flex items-center text-gray-500 text-xs mt-1">
                      <MapPin className="h-3 w-3 mr-1" />
                      {client.location}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900" style={{ width: '150px' }}>
                  <div>
                    <div>{client.os}</div>
                    <div className="text-gray-500">v{client.version}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900" style={{ width: '160px' }}>
                  <div>
                    <div className="font-medium">{client.lastOnline}</div>
                    <div className="text-gray-500">{formatUptime(client.uptime)}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap" style={{ width: '120px' }}>
                  <div className="flex flex-wrap gap-1">
                    {client.tags.map((tag, index) => (
                      <Badge key={index} variant="info" size="sm">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium" style={{ width: '80px' }}>
                  <div className="relative dropdown-container">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setOpenDropdown(openDropdown === client.id ? null : client.id)}
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>

                    {openDropdown === client.id && (
                      <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                        <button
                          onClick={() => {
                            onRemoteConnect(client.id)
                            setOpenDropdown(null)
                          }}
                          disabled={client.status !== 'online'}
                          className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Monitor className="h-4 w-4 mr-3" />
                          远程桌面
                        </button>
                        <button
                          onClick={() => {
                            onFileManage(client.id)
                            setOpenDropdown(null)
                          }}
                          disabled={client.status !== 'online'}
                          className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <FolderOpen className="h-4 w-4 mr-3" />
                          文件管理
                        </button>
                        <button
                          onClick={() => {
                            onCmdExecute(client.id)
                            setOpenDropdown(null)
                          }}
                          disabled={client.status !== 'online'}
                          className="w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Terminal className="h-4 w-4 mr-3" />
                          CMD命令
                        </button>
                      </div>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}

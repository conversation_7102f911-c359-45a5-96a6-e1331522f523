'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useLog } from '@/contexts/LogContext'
import {
  Monitor,
  Users,
  Activity,
  FileText,
  Settings,
  Shield,
  ChevronDown,
  ChevronRight,
  X,
  FolderOpen,
  Terminal
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { NavigationItem } from '@/types'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
}

const navigationItems: NavigationItem[] = [
  {
    id: 'clients',
    label: '客户端管理',
    icon: 'Monitor',
    href: '/',
    badge: 12
  },
  {
    id: 'remote',
    label: '远程桌面',
    icon: 'Monitor',
    href: '/remote'
  },
  {
    id: 'files',
    label: '文件管理',
    icon: 'FolderOpen',
    href: '/files'
  },
  {
    id: 'cmd',
    label: 'CMD命令',
    icon: 'Terminal',
    href: '/cmd'
  },
  {
    id: 'monitoring',
    label: '资源监控',
    icon: 'Activity',
    href: '/monitoring'
  },
  {
    id: 'logs',
    label: '日志记录',
    icon: 'FileText',
    href: '/logs'
  },
  {
    id: 'users',
    label: '用户管理',
    icon: 'Users',
    href: '/users'
  },
  {
    id: 'settings',
    label: '系统设置',
    icon: 'Settings',
    href: '/settings'
  }
]

const iconMap = {
  Monitor,
  Users,
  Activity,
  FileText,
  Settings,
  Shield,
  FolderOpen,
  Terminal
}

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>(['settings'])
  const { getRecentLogs } = useLog()

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const Icon = iconMap[item.icon as keyof typeof iconMap]
    const isActive = pathname === item.href
    const isExpanded = expandedItems.includes(item.id)
    const hasChildren = item.children && item.children.length > 0

    return (
      <div key={item.id}>
        <div
          className={cn(
            'flex items-center justify-between px-2 py-1.5 rounded-lg text-sm font-medium transition-colors',
            level > 0 && 'ml-3',
            isActive
              ? 'bg-blue-100 text-blue-700'
              : 'text-gray-700 hover:bg-gray-100'
          )}
        >
          <Link
            href={item.href}
            className="flex items-center flex-1"
            onClick={() => {
              if (window.innerWidth < 1024) {
                onClose()
              }
            }}
          >
            <Icon className="h-4 w-4 mr-2" />
            <span>{item.label}</span>
            {item.badge && (
              <span className="ml-auto bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                {item.badge}
              </span>
            )}
          </Link>
          
          {hasChildren && (
            <button
              onClick={() => toggleExpanded(item.id)}
              className="p-1 hover:bg-gray-200 rounded"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>
          )}
        </div>

        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      {/* 移动端遮罩 */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* 侧边栏 */}
      <aside
        className={cn(
          'fixed top-0 left-0 z-50 h-full w-52 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:z-auto lg:h-auto',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between p-3 border-b border-gray-200 lg:hidden">
          <h2 className="text-lg font-semibold text-gray-900">导航菜单</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-md hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 导航菜单 */}
        <nav className="flex flex-col h-full">
          <div className="flex-1 p-3 space-y-1 overflow-y-auto">
            {navigationItems.map(item => renderNavigationItem(item))}
          </div>

          {/* 操作日志区域 */}
          <div className="p-3 border-t border-gray-200">
            <div className="bg-gray-50 rounded-lg p-2">
              <h4 className="text-xs font-medium text-gray-700 mb-2">操作日志</h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {getRecentLogs(4).map((log) => (
                  <div key={log.id} className="text-xs">
                    <span className="text-gray-500">{log.time}</span>
                    <span className={`ml-1 ${
                      log.type === 'success' ? 'text-green-600' :
                      log.type === 'warning' ? 'text-yellow-600' :
                      log.type === 'error' ? 'text-red-600' :
                      'text-blue-600'
                    }`}>
                      {log.message}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 底部状态信息 */}
          <div className="p-3 border-t border-gray-200">
            <div className="bg-gray-50 rounded-lg p-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-gray-600">在线客户端</span>
                <span className="font-medium text-green-600">12/15</span>
              </div>
              <div className="flex items-center justify-between text-xs mt-1">
                <span className="text-gray-600">系统状态</span>
                <span className="font-medium text-green-600">正常</span>
              </div>
            </div>
          </div>
        </nav>
      </aside>
    </>
  )
}

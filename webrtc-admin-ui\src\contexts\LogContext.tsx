'use client'

import { createContext, useContext, useState, ReactNode } from 'react'

export interface LogEntry {
  id: string
  time: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  module?: string
}

interface LogContextType {
  logs: LogEntry[]
  addLog: (message: string, type: LogEntry['type'], module?: string) => void
  clearLogs: () => void
  getRecentLogs: (count?: number) => LogEntry[]
}

const LogContext = createContext<LogContextType | undefined>(undefined)

export function LogProvider({ children }: { children: ReactNode }) {
  const [logs, setLogs] = useState<LogEntry[]>([
    {
      id: '1',
      time: '14:30:15',
      message: '系统启动完成',
      type: 'success',
      module: 'system'
    },
    {
      id: '2',
      time: '14:30:18',
      message: '客户端连接成功',
      type: 'success',
      module: 'remote'
    },
    {
      id: '3',
      time: '14:30:20',
      message: '开始接收视频流',
      type: 'info',
      module: 'remote'
    }
  ])

  const addLog = (message: string, type: LogEntry['type'], module?: string) => {
    const newLog: LogEntry = {
      id: Date.now().toString(),
      time: new Date().toLocaleTimeString('zh-CN', { 
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      message,
      type,
      module
    }
    
    setLogs(prev => [newLog, ...prev].slice(0, 100)) // 保留最近100条日志
  }

  const clearLogs = () => {
    setLogs([])
  }

  const getRecentLogs = (count = 5) => {
    return logs.slice(0, count)
  }

  return (
    <LogContext.Provider value={{ logs, addLog, clearLogs, getRecentLogs }}>
      {children}
    </LogContext.Provider>
  )
}

export function useLog() {
  const context = useContext(LogContext)
  if (context === undefined) {
    throw new Error('useLog must be used within a LogProvider')
  }
  return context
}

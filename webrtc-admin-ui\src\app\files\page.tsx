'use client'

import React, { useState } from 'react'
import {
  FolderOpen,
  File,
  Download,
  Upload,
  Trash2,
  Edit,
  Copy,
  Move,
  Search,
  RefreshCw,
  Home,
  ChevronRight,
  FileText,
  Image,
  Video,
  Music,
  Archive,
  Folder,
  ChevronDown,
  HardDrive,
  Monitor,
  Wifi,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  MoreHorizontal,
  Grid3X3,
  List,
  SortAsc
} from 'lucide-react'
import ClientSelector from '@/components/remote/ClientSelector'
import Badge from '@/components/ui/Badge'

interface Client {
  id: string
  name: string
  ip: string
  status: 'online' | 'offline'
  os: string
  location: string
  uuid: string
  owner: {
    name: string
    uuid: string
  }
  group: string
}

interface FileItem {
  id: string
  name: string
  type: 'folder' | 'file'
  size?: number
  modified: string
  extension?: string
  path: string
  children?: FileItem[]
}

interface TreeNode {
  id: string
  name: string
  path: string
  type: 'drive' | 'folder'
  children?: TreeNode[]
  expanded?: boolean
}

// 树形目录结构
const mockTreeData: TreeNode[] = [
  {
    id: 'c',
    name: '本地磁盘 (C:)',
    path: 'C:',
    type: 'drive',
    expanded: true,
    children: [
      {
        id: 'users',
        name: 'Users',
        path: 'C:/Users',
        type: 'folder',
        expanded: true,
        children: [
          {
            id: 'admin',
            name: 'Administrator',
            path: 'C:/Users/<USER>',
            type: 'folder',
            expanded: true,
            children: [
              {
                id: 'desktop',
                name: 'Desktop',
                path: 'C:/Users/<USER>/Desktop',
                type: 'folder'
              },
              {
                id: 'documents',
                name: 'Documents',
                path: 'C:/Users/<USER>/Documents',
                type: 'folder'
              },
              {
                id: 'downloads',
                name: 'Downloads',
                path: 'C:/Users/<USER>/Downloads',
                type: 'folder'
              },
              {
                id: 'pictures',
                name: 'Pictures',
                path: 'C:/Users/<USER>/Pictures',
                type: 'folder'
              }
            ]
          }
        ]
      },
      {
        id: 'program-files',
        name: 'Program Files',
        path: 'C:/Program Files',
        type: 'folder'
      },
      {
        id: 'windows',
        name: 'Windows',
        path: 'C:/Windows',
        type: 'folder'
      }
    ]
  },
  {
    id: 'd',
    name: '本地磁盘 (D:)',
    path: 'D:',
    type: 'drive',
    children: [
      {
        id: 'data',
        name: 'Data',
        path: 'D:/Data',
        type: 'folder'
      },
      {
        id: 'backup',
        name: 'Backup',
        path: 'D:/Backup',
        type: 'folder'
      }
    ]
  }
]

// 根据路径获取文件列表的模拟数据
const getFilesForPath = (path: string): FileItem[] => {
  const pathMap: { [key: string]: FileItem[] } = {
    'C:/Users/<USER>': [
      {
        id: '1',
        name: 'Desktop',
        type: 'folder',
        modified: '2024-01-15 14:30',
        path: 'C:/Users/<USER>/Desktop'
      },
      {
        id: '2',
        name: 'Documents',
        type: 'folder',
        modified: '2024-01-15 10:20',
        path: 'C:/Users/<USER>/Documents'
      },
      {
        id: '3',
        name: 'Downloads',
        type: 'folder',
        modified: '2024-01-14 16:45',
        path: 'C:/Users/<USER>/Downloads'
      },
      {
        id: '4',
        name: 'Pictures',
        type: 'folder',
        modified: '2024-01-14 15:30',
        path: 'C:/Users/<USER>/Pictures'
      }
    ],
    'C:/Users/<USER>/Documents': [
      {
        id: '5',
        name: 'report.pdf',
        type: 'file',
        size: 2048576,
        modified: '2024-01-15 09:15',
        extension: 'pdf',
        path: 'C:/Users/<USER>/Documents/report.pdf'
      },
      {
        id: '6',
        name: 'presentation.pptx',
        type: 'file',
        size: 5242880,
        modified: '2024-01-14 15:30',
        extension: 'pptx',
        path: 'C:/Users/<USER>/Documents/presentation.pptx'
      },
      {
        id: '7',
        name: 'data.xlsx',
        type: 'file',
        size: 1048576,
        modified: '2024-01-13 11:20',
        extension: 'xlsx',
        path: 'C:/Users/<USER>/Documents/data.xlsx'
      }
    ],
    'C:/Users/<USER>/Downloads': [
      {
        id: '8',
        name: 'setup.exe',
        type: 'file',
        size: 15728640,
        modified: '2024-01-15 16:20',
        extension: 'exe',
        path: 'C:/Users/<USER>/Downloads/setup.exe'
      },
      {
        id: '9',
        name: 'archive.zip',
        type: 'file',
        size: 8388608,
        modified: '2024-01-14 12:45',
        extension: 'zip',
        path: 'C:/Users/<USER>/Downloads/archive.zip'
      }
    ],
    'C:/Users/<USER>/Pictures': [
      {
        id: '10',
        name: 'photo1.jpg',
        type: 'file',
        size: 3145728,
        modified: '2024-01-12 18:30',
        extension: 'jpg',
        path: 'C:/Users/<USER>/Pictures/photo1.jpg'
      },
      {
        id: '11',
        name: 'screenshot.png',
        type: 'file',
        size: 1572864,
        modified: '2024-01-13 09:15',
        extension: 'png',
        path: 'C:/Users/<USER>/Pictures/screenshot.png'
      }
    ]
  }

  return pathMap[path] || []
}

export default function FilesPage() {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [currentPath, setCurrentPath] = useState('C:/Users/<USER>')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')
  const [treeData, setTreeData] = useState<TreeNode[]>(mockTreeData)
  const [navigationHistory, setNavigationHistory] = useState<string[]>(['C:/Users/<USER>'])
  const [historyIndex, setHistoryIndex] = useState(0)

  // 导航相关函数
  const navigateToPath = (path: string) => {
    setCurrentPath(path)
    const newHistory = navigationHistory.slice(0, historyIndex + 1)
    newHistory.push(path)
    setNavigationHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
  }

  const goBack = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setCurrentPath(navigationHistory[historyIndex - 1])
    }
  }

  const goForward = () => {
    if (historyIndex < navigationHistory.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setCurrentPath(navigationHistory[historyIndex + 1])
    }
  }

  const goUp = () => {
    const pathParts = currentPath.split('/').filter(Boolean)
    if (pathParts.length > 1) {
      const parentPath = pathParts.slice(0, -1).join('/')
      navigateToPath(parentPath.startsWith('C:') || parentPath.startsWith('D:') ? parentPath : 'C:' + parentPath)
    }
  }

  // 树形目录相关函数
  const toggleTreeNode = (nodeId: string) => {
    const updateNode = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.map(node => {
        if (node.id === nodeId) {
          return { ...node, expanded: !node.expanded }
        }
        if (node.children) {
          return { ...node, children: updateNode(node.children) }
        }
        return node
      })
    }
    setTreeData(updateNode(treeData))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (item: FileItem) => {
    if (item.type === 'folder') {
      return <Folder className="h-4 w-4 text-blue-600" />
    }

    const ext = item.extension?.toLowerCase()
    switch (ext) {
      case 'pdf':
      case 'doc':
      case 'docx':
      case 'txt':
        return <FileText className="h-4 w-4 text-red-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <Image className="h-4 w-4 text-green-500" />
      case 'mp4':
      case 'avi':
      case 'mov':
        return <Video className="h-4 w-4 text-purple-500" />
      case 'mp3':
      case 'wav':
      case 'flac':
        return <Music className="h-4 w-4 text-orange-500" />
      case 'zip':
      case 'rar':
      case '7z':
        return <Archive className="h-4 w-4 text-yellow-500" />
      case 'exe':
        return <File className="h-4 w-4 text-blue-500" />
      default:
        return <File className="h-4 w-4 text-gray-500" />
    }
  }

  const getTreeIcon = (node: TreeNode) => {
    if (node.type === 'drive') {
      return <HardDrive className="h-4 w-4 text-gray-600" />
    }
    return <Folder className="h-4 w-4 text-blue-600" />
  }

  // 获取当前路径的文件列表
  const currentFiles = getFilesForPath(currentPath)
  const filteredFiles = currentFiles.filter(file =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 路径分段处理
  const pathSegments = currentPath.split(/[:/\\]/).filter(Boolean)

  // 渲染树形节点
  const renderTreeNode = (node: TreeNode, level = 0) => (
    <div key={node.id}>
      <div
        className={`flex items-center py-1 px-2 hover:bg-gray-100 cursor-pointer text-sm ${
          currentPath === node.path ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
        }`}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={() => {
          if (node.type === 'folder') {
            navigateToPath(node.path)
          } else {
            navigateToPath(node.path)
          }
        }}
      >
        {node.children && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              toggleTreeNode(node.id)
            }}
            className="mr-1 p-0.5 hover:bg-gray-200 rounded"
          >
            <ChevronRight
              className={`h-3 w-3 transition-transform ${
                node.expanded ? 'rotate-90' : ''
              }`}
            />
          </button>
        )}
        {!node.children && <div className="w-4" />}
        {getTreeIcon(node)}
        <span className="ml-2 truncate">{node.name}</span>
      </div>
      {node.expanded && node.children && (
        <div>
          {node.children.map(child => renderTreeNode(child, level + 1))}
        </div>
      )}
    </div>
  )

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* 顶部标题栏 */}
      <div className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-semibold text-gray-900">文件管理</h1>
            {selectedClient && (
              <div className="flex items-center text-sm text-gray-600">
                <Monitor className="h-4 w-4 mr-1" />
                <span>{selectedClient.name}</span>
                <Wifi className="h-4 w-4 ml-2 text-green-500" />
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <button className="px-3 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 flex items-center">
              <Upload className="h-4 w-4 mr-1" />
              上传
            </button>
            <button className="px-3 py-1.5 bg-green-600 text-white rounded text-sm hover:bg-green-700 flex items-center">
              <FolderOpen className="h-4 w-4 mr-1" />
              新建文件夹
            </button>
          </div>
        </div>
      </div>

      {/* 客户端选择器 */}
      {!selectedClient && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-3">
          <ClientSelector
            selectedClient={selectedClient}
            onClientSelect={setSelectedClient}
          />
        </div>
      )}

      {/* 工具栏 */}
      {selectedClient && (
        <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
          <div className="flex items-center justify-between">
            {/* 导航按钮 */}
            <div className="flex items-center space-x-1">
              <button
                onClick={goBack}
                disabled={historyIndex <= 0}
                className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowLeft className="h-4 w-4" />
              </button>
              <button
                onClick={goForward}
                disabled={historyIndex >= navigationHistory.length - 1}
                className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowRight className="h-4 w-4" />
              </button>
              <button
                onClick={goUp}
                className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded"
              >
                <ArrowUp className="h-4 w-4" />
              </button>
              <button className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded">
                <RefreshCw className="h-4 w-4" />
              </button>
            </div>

            {/* 视图切换和其他工具 */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setViewMode('list')}
                className={`p-1.5 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-200'}`}
              >
                <List className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-1.5 rounded ${viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-200'}`}
              >
                <Grid3X3 className="h-4 w-4" />
              </button>
              <button className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded">
                <SortAsc className="h-4 w-4" />
              </button>
              <button className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded">
                <MoreHorizontal className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 地址栏 */}
      {selectedClient && (
        <div className="bg-white border-b border-gray-200 px-4 py-2">
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-sm">
              {pathSegments.map((segment, index) => (
                <div key={index} className="flex items-center">
                  {index > 0 && <ChevronRight className="h-3 w-3 text-gray-400 mx-1" />}
                  <button
                    onClick={() => {
                      const newPath = pathSegments.slice(0, index + 1).join('/')
                      navigateToPath(newPath.includes(':') ? newPath : 'C:/' + newPath)
                    }}
                    className="px-2 py-1 text-gray-700 hover:bg-gray-100 rounded"
                  >
                    {segment}
                  </button>
                </div>
              ))}
            </div>
            <div className="flex-1" />
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索文件..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-1.5 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-64"
              />
            </div>
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {selectedClient ? (
          <>
            {/* 左侧树形目录 */}
            <div className="w-64 bg-white border-r border-gray-200 overflow-y-auto">
              <div className="p-2">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2 px-2">
                  此电脑
                </div>
                {treeData.map(node => renderTreeNode(node))}
              </div>
            </div>

            {/* 右侧文件列表 */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* 文件列表头部 */}
              <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {filteredFiles.length} 个项目
                  </span>
                  {selectedFiles.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-blue-600">已选择 {selectedFiles.length} 个项目</span>
                      <button className="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700">
                        删除
                      </button>
                      <button className="px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700">
                        下载
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* 文件列表内容 */}
              <div className="flex-1 overflow-auto">
                {viewMode === 'list' ? (
                  /* 列表视图 */
                  <table className="w-full">
                    <thead className="bg-gray-50 sticky top-0 border-b border-gray-200">
                      <tr>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8">
                          <input
                            type="checkbox"
                            className="rounded border-gray-300"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedFiles(filteredFiles.map(f => f.id))
                              } else {
                                setSelectedFiles([])
                              }
                            }}
                          />
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          名称
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                          大小
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                          修改时间
                        </th>
                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                          操作
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-100">
                      {filteredFiles.map((file) => (
                        <tr
                          key={file.id}
                          className={`hover:bg-gray-50 cursor-pointer ${
                            selectedFiles.includes(file.id) ? 'bg-blue-50' : ''
                          }`}
                          onDoubleClick={() => {
                            if (file.type === 'folder') {
                              navigateToPath(file.path)
                            }
                          }}
                        >
                          <td className="px-4 py-2">
                            <input
                              type="checkbox"
                              className="rounded border-gray-300"
                              checked={selectedFiles.includes(file.id)}
                              onChange={(e) => {
                                e.stopPropagation()
                                if (e.target.checked) {
                                  setSelectedFiles([...selectedFiles, file.id])
                                } else {
                                  setSelectedFiles(selectedFiles.filter(id => id !== file.id))
                                }
                              }}
                            />
                          </td>
                          <td className="px-4 py-2">
                            <div className="flex items-center">
                              {getFileIcon(file)}
                              <span className="ml-2 text-sm text-gray-900 truncate">
                                {file.name}
                              </span>
                            </div>
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-500">
                            {file.type === 'file' && file.size ? formatFileSize(file.size) : ''}
                          </td>
                          <td className="px-4 py-2 text-sm text-gray-500">
                            {file.modified}
                          </td>
                          <td className="px-4 py-2">
                            <div className="flex items-center space-x-1">
                              <button className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded">
                                <Download className="h-3 w-3" />
                              </button>
                              <button className="p-1 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded">
                                <Edit className="h-3 w-3" />
                              </button>
                              <button className="p-1 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded">
                                <Copy className="h-3 w-3" />
                              </button>
                              <button className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded">
                                <Trash2 className="h-3 w-3" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  /* 网格视图 */
                  <div className="p-4 grid grid-cols-6 gap-4">
                    {filteredFiles.map((file) => (
                      <div
                        key={file.id}
                        className={`p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer text-center ${
                          selectedFiles.includes(file.id) ? 'bg-blue-50 border-blue-300' : ''
                        }`}
                        onDoubleClick={() => {
                          if (file.type === 'folder') {
                            navigateToPath(file.path)
                          }
                        }}
                        onClick={() => {
                          if (selectedFiles.includes(file.id)) {
                            setSelectedFiles(selectedFiles.filter(id => id !== file.id))
                          } else {
                            setSelectedFiles([...selectedFiles, file.id])
                          }
                        }}
                      >
                        <div className="flex justify-center mb-2">
                          {React.cloneElement(getFileIcon(file), { className: 'h-8 w-8' })}
                        </div>
                        <div className="text-xs text-gray-900 truncate" title={file.name}>
                          {file.name}
                        </div>
                        {file.type === 'file' && file.size && (
                          <div className="text-xs text-gray-500 mt-1">
                            {formatFileSize(file.size)}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {/* 空状态 */}
                {filteredFiles.length === 0 && (
                  <div className="flex-1 flex items-center justify-center">
                    <div className="text-center">
                      <Folder className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-sm font-medium text-gray-900 mb-2">此文件夹为空</h3>
                      <p className="text-sm text-gray-500">
                        {searchTerm ? '没有找到匹配的文件' : '此文件夹中没有任何文件或文件夹'}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <FolderOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">选择客户端</h3>
              <p className="text-gray-500">请先选择一个客户端来管理文件</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

'use client'

import { useState } from 'react'
import { 
  FolderOpen, 
  File, 
  Download, 
  Upload, 
  Trash2, 
  Edit, 
  Copy,
  Move,
  Search,
  RefreshCw,
  Home,
  ChevronRight,
  FileText,
  Image,
  Video,
  Music,
  Archive
} from 'lucide-react'
import ClientSelector from '@/components/remote/ClientSelector'
import Badge from '@/components/ui/Badge'

interface Client {
  id: string
  name: string
  ip: string
  status: 'online' | 'offline'
  os: string
  location: string
  uuid: string
  owner: {
    name: string
    uuid: string
  }
  group: string
}

interface FileItem {
  id: string
  name: string
  type: 'folder' | 'file'
  size?: number
  modified: string
  extension?: string
  path: string
}

const mockFiles: FileItem[] = [
  {
    id: '1',
    name: 'Documents',
    type: 'folder',
    modified: '2024-01-15 14:30',
    path: '/Documents'
  },
  {
    id: '2',
    name: 'Downloads',
    type: 'folder',
    modified: '2024-01-15 10:20',
    path: '/Downloads'
  },
  {
    id: '3',
    name: 'Pictures',
    type: 'folder',
    modified: '2024-01-14 16:45',
    path: '/Pictures'
  },
  {
    id: '4',
    name: 'report.pdf',
    type: 'file',
    size: 2048576,
    modified: '2024-01-15 09:15',
    extension: 'pdf',
    path: '/report.pdf'
  },
  {
    id: '5',
    name: 'presentation.pptx',
    type: 'file',
    size: 5242880,
    modified: '2024-01-14 15:30',
    extension: 'pptx',
    path: '/presentation.pptx'
  },
  {
    id: '6',
    name: 'data.xlsx',
    type: 'file',
    size: 1048576,
    modified: '2024-01-13 11:20',
    extension: 'xlsx',
    path: '/data.xlsx'
  }
]

export default function FilesPage() {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [currentPath, setCurrentPath] = useState('/')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (item: FileItem) => {
    if (item.type === 'folder') {
      return <FolderOpen className="h-5 w-5 text-blue-500" />
    }
    
    const ext = item.extension?.toLowerCase()
    switch (ext) {
      case 'pdf':
      case 'doc':
      case 'docx':
      case 'txt':
        return <FileText className="h-5 w-5 text-red-500" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <Image className="h-5 w-5 text-green-500" />
      case 'mp4':
      case 'avi':
      case 'mov':
        return <Video className="h-5 w-5 text-purple-500" />
      case 'mp3':
      case 'wav':
      case 'flac':
        return <Music className="h-5 w-5 text-orange-500" />
      case 'zip':
      case 'rar':
      case '7z':
        return <Archive className="h-5 w-5 text-yellow-500" />
      default:
        return <File className="h-5 w-5 text-gray-500" />
    }
  }

  const filteredFiles = mockFiles.filter(file =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const pathSegments = currentPath.split('/').filter(Boolean)

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 顶部工具栏 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900">文件管理</h1>
          <div className="flex items-center space-x-2">
            <button className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
              <Upload className="h-4 w-4 mr-2" />
              上传文件
            </button>
            <button className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center">
              <FolderOpen className="h-4 w-4 mr-2" />
              新建文件夹
            </button>
          </div>
        </div>

        {/* 客户端选择器 */}
        <div className="mb-4">
          <ClientSelector
            selectedClient={selectedClient}
            onClientSelect={setSelectedClient}
          />
        </div>

        {/* 路径导航 */}
        <div className="flex items-center space-x-2 mb-4">
          <button
            onClick={() => setCurrentPath('/')}
            className="flex items-center px-2 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
          >
            <Home className="h-4 w-4 mr-1" />
            根目录
          </button>
          {pathSegments.map((segment, index) => (
            <div key={index} className="flex items-center">
              <ChevronRight className="h-4 w-4 text-gray-400" />
              <button
                onClick={() => setCurrentPath('/' + pathSegments.slice(0, index + 1).join('/'))}
                className="px-2 py-1 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
              >
                {segment}
              </button>
            </div>
          ))}
        </div>

        {/* 搜索和工具栏 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索文件和文件夹..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded">
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>

          <div className="flex items-center space-x-2">
            {selectedFiles.length > 0 && (
              <>
                <button className="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center">
                  <Trash2 className="h-4 w-4 mr-2" />
                  删除 ({selectedFiles.length})
                </button>
                <button className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center">
                  <Download className="h-4 w-4 mr-2" />
                  下载
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {selectedClient ? (
          <div className="flex-1 p-4">
            {/* 文件列表 */}
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300"
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedFiles(filteredFiles.map(f => f.id))
                            } else {
                              setSelectedFiles([])
                            }
                          }}
                        />
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        名称
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        大小
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        修改时间
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredFiles.map((file) => (
                      <tr
                        key={file.id}
                        className={`hover:bg-gray-50 ${
                          selectedFiles.includes(file.id) ? 'bg-blue-50' : ''
                        }`}
                      >
                        <td className="px-4 py-3">
                          <input
                            type="checkbox"
                            className="rounded border-gray-300"
                            checked={selectedFiles.includes(file.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedFiles([...selectedFiles, file.id])
                              } else {
                                setSelectedFiles(selectedFiles.filter(id => id !== file.id))
                              }
                            }}
                          />
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            {getFileIcon(file)}
                            <span className="ml-3 text-sm font-medium text-gray-900">
                              {file.name}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">
                          {file.type === 'file' && file.size ? formatFileSize(file.size) : '-'}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">
                          {file.modified}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-2">
                            <button className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded">
                              <Download className="h-4 w-4" />
                            </button>
                            <button className="p-1 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded">
                              <Edit className="h-4 w-4" />
                            </button>
                            <button className="p-1 text-gray-600 hover:text-yellow-600 hover:bg-yellow-50 rounded">
                              <Copy className="h-4 w-4" />
                            </button>
                            <button className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded">
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <FolderOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">选择客户端</h3>
              <p className="text-gray-500">请先选择一个客户端来管理文件</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

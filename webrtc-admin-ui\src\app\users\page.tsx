'use client'

import { useState } from 'react'
import { 
  Plus, 
  Search, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Shield, 
  User, 
  Clock,
  MapPin,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { User as UserType } from '@/types'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'

// 模拟用户数据
const mockUsers: UserType[] = [
  {
    id: 'user-001',
    username: 'admin',
    role: 'admin',
    lastLogin: '2024-01-15 14:30:00',
    loginIP: '************',
    isActive: true,
    permissions: ['all']
  },
  {
    id: 'user-002',
    username: 'operator1',
    role: 'operator',
    lastLogin: '2024-01-15 13:45:00',
    loginIP: '************',
    isActive: true,
    permissions: ['remote_control', 'view_logs', 'file_transfer']
  },
  {
    id: 'user-003',
    username: 'viewer1',
    role: 'viewer',
    lastLogin: '2024-01-15 12:20:00',
    loginIP: '************',
    isActive: true,
    permissions: ['view_clients', 'view_logs']
  },
  {
    id: 'user-004',
    username: 'operator2',
    role: 'operator',
    lastLogin: '2024-01-14 16:30:00',
    loginIP: '************',
    isActive: false,
    permissions: ['remote_control', 'view_logs']
  }
]

const roleLabels = {
  admin: '系统管理员',
  operator: '操作员',
  viewer: '查看者'
}

const roleColors = {
  admin: 'danger' as const,
  operator: 'warning' as const,
  viewer: 'info' as const
}

const permissionLabels = {
  all: '全部权限',
  remote_control: '远程控制',
  view_clients: '查看客户端',
  view_logs: '查看日志',
  file_transfer: '文件传输',
  user_management: '用户管理',
  system_settings: '系统设置'
}

export default function UsersPage() {
  const [users] = useState<UserType[]>(mockUsers)
  const [searchQuery, setSearchQuery] = useState('')
  const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'operator' | 'viewer'>('all')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [showAddModal, setShowAddModal] = useState(false)

  // 筛选用户
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesRole = roleFilter === 'all' || user.role === roleFilter
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'active' && user.isActive) ||
      (statusFilter === 'inactive' && !user.isActive)
    
    return matchesSearch && matchesRole && matchesStatus
  })

  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  const handleSelectAll = (selected: boolean) => {
    setSelectedUsers(selected ? filteredUsers.map(user => user.id) : [])
  }

  const getRoleBadge = (role: UserType['role']) => {
    return (
      <Badge variant={roleColors[role]}>
        {roleLabels[role]}
      </Badge>
    )
  }

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="success">活跃</Badge>
    ) : (
      <Badge variant="default">禁用</Badge>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">用户管理</h1>
            <p className="text-sm text-gray-500 mt-1">
              管理系统用户账户和权限设置
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button size="sm" onClick={() => setShowAddModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              添加用户
            </Button>
          </div>
        </div>
      </div>

      {/* 筛选栏 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* 搜索框 */}
          <div className="md:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索用户名..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 placeholder-gray-500 bg-white"
              />
            </div>
          </div>

          {/* 角色筛选 */}
          <div>
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value as any)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white"
            >
              <option value="all">全部角色</option>
              <option value="admin">系统管理员</option>
              <option value="operator">操作员</option>
              <option value="viewer">查看者</option>
            </select>
          </div>

          {/* 状态筛选 */}
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
            >
              <option value="all">全部状态</option>
              <option value="active">活跃</option>
              <option value="inactive">禁用</option>
            </select>
          </div>
        </div>

        {/* 批量操作 */}
        {selectedUsers.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-700">
                已选择 {selectedUsers.length} 个用户
              </span>
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="outline">
                  批量启用
                </Button>
                <Button size="sm" variant="outline">
                  批量禁用
                </Button>
                <Button size="sm" variant="danger">
                  批量删除
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 用户表格 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={filteredUsers.length > 0 && selectedUsers.length === filteredUsers.length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  用户
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  角色
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  最后登录
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  权限
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map((user) => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => handleSelectUser(user.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                        <User className="h-5 w-5 text-gray-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.username}</div>
                        <div className="text-sm text-gray-500">{user.id}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getRoleBadge(user.role)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {user.isActive ? (
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500 mr-2" />
                      )}
                      {getStatusBadge(user.isActive)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-gray-400 mr-2" />
                      <div>
                        <div>{user.lastLogin}</div>
                        <div className="text-xs text-gray-500 flex items-center mt-1">
                          <MapPin className="h-3 w-3 mr-1" />
                          {user.loginIP}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1 max-w-xs">
                      {user.permissions.slice(0, 3).map((permission, index) => (
                        <Badge key={index} variant="default" size="sm">
                          {permissionLabels[permission as keyof typeof permissionLabels] || permission}
                        </Badge>
                      ))}
                      {user.permissions.length > 3 && (
                        <Badge variant="default" size="sm">
                          +{user.permissions.length - 3}
                        </Badge>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                      <Button size="sm" variant="outline">
                        <Shield className="h-4 w-4 mr-1" />
                        权限
                      </Button>
                      <Button size="sm" variant="outline">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 权限说明 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">权限说明</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="text-sm font-medium text-red-700 mb-2">系统管理员</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 完全系统访问权限</li>
              <li>• 用户管理</li>
              <li>• 系统设置</li>
              <li>• 所有操作权限</li>
            </ul>
          </div>
          <div>
            <h4 className="text-sm font-medium text-yellow-700 mb-2">操作员</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 远程控制客户端</li>
              <li>• 文件传输</li>
              <li>• 查看日志</li>
              <li>• 基本操作权限</li>
            </ul>
          </div>
          <div>
            <h4 className="text-sm font-medium text-blue-700 mb-2">查看者</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 查看客户端状态</li>
              <li>• 查看系统日志</li>
              <li>• 只读权限</li>
              <li>• 无操作权限</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

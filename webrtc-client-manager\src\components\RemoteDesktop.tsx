'use client'

import { useState, useRef, useEffect } from 'react'
import {
  Monitor,
  WifiOff,
  Volume2,
  VolumeX,
  Settings,
  Maximize,
  Minimize,
  Power,
  RotateCcw,
  Pause,
  Play
} from 'lucide-react'
import { Client, RemoteSession } from '@/types/client'

interface RemoteDesktopProps {
  client: Client | null
}

export default function RemoteDesktop({ client }: RemoteDesktopProps) {
  const [session, setSession] = useState<RemoteSession>({
    clientId: '',
    clientName: '',
    clientIP: '',
    status: 'disconnected',
    quality: 'high',
    resolution: '1920x1080',
    startTime: ''
  })

  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 当客户端改变时，自动连接
  useEffect(() => {
    if (client && client.status === 'online') {
      setSession(prev => ({
        ...prev,
        clientId: client.id,
        clientName: client.name,
        clientIP: client.ip,
        status: 'connecting',
        startTime: new Date().toLocaleString()
      }))

      // 模拟连接过程
      setTimeout(() => {
        setSession(prev => ({ ...prev, status: 'connected' }))
      }, 1500)
    } else {
      setSession(prev => ({ ...prev, status: 'disconnected' }))
    }
  }, [client])

  const handleFullscreen = () => {
    if (!isFullscreen) {
      containerRef.current?.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
    setIsFullscreen(!isFullscreen)
  }

  const handleDisconnect = () => {
    setSession(prev => ({ ...prev, status: 'disconnected' }))
  }

  const handleReconnect = () => {
    if (!client) return
    
    setSession(prev => ({ 
      ...prev, 
      status: 'connecting',
      startTime: new Date().toLocaleString()
    }))

    setTimeout(() => {
      setSession(prev => ({ ...prev, status: 'connected' }))
    }, 2000)
  }

  const handlePause = () => {
    setIsPaused(!isPaused)
  }

  if (!client) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Monitor className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">远程桌面</h3>
          <p className="text-gray-500">请从左侧选择一个客户端开始远程桌面连接</p>
        </div>
      </div>
    )
  }

  if (client.status === 'offline') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <WifiOff className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">客户端离线</h3>
          <p className="text-gray-500">客户端 {client.name} 当前离线，无法建立远程桌面连接</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-50">
      {/* 控制栏 */}
      <div className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-sm font-medium text-gray-900">
              远程桌面连接 - {client.name}
            </h3>
            {session.status === 'connected' && (
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <span>分辨率: {session.resolution}</span>
                <span>质量: {session.quality.toUpperCase()}</span>
                <span>延迟: 45ms</span>
                <span>帧率: 60fps</span>
                <span>连接时间: {session.startTime}</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {session.status === 'connected' && (
              <>
                <button
                  onClick={() => setIsMuted(!isMuted)}
                  className={`p-2 rounded text-xs ${
                    isMuted ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-700'
                  } hover:bg-opacity-80`}
                  title={isMuted ? '取消静音' : '静音'}
                >
                  {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                </button>

                <button
                  onClick={handlePause}
                  className={`p-2 rounded text-xs ${
                    isPaused ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-100 text-gray-700'
                  } hover:bg-opacity-80`}
                  title={isPaused ? '恢复' : '暂停'}
                >
                  {isPaused ? <Play className="h-4 w-4" /> : <Pause className="h-4 w-4" />}
                </button>

                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="p-2 rounded text-xs bg-gray-100 text-gray-700 hover:bg-gray-200"
                  title="设置"
                >
                  <Settings className="h-4 w-4" />
                </button>

                <button
                  onClick={handleFullscreen}
                  className="p-2 rounded text-xs bg-gray-100 text-gray-700 hover:bg-gray-200"
                  title={isFullscreen ? '退出全屏' : '全屏'}
                >
                  {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
                </button>

                <button
                  onClick={handleDisconnect}
                  className="p-2 rounded text-xs bg-red-100 text-red-700 hover:bg-red-200"
                  title="断开连接"
                >
                  <Power className="h-4 w-4" />
                </button>
              </>
            )}

            {session.status === 'disconnected' && (
              <button
                onClick={handleReconnect}
                className="px-3 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 flex items-center"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                连接
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 主视频区域 */}
      <div className="flex-1 bg-black overflow-hidden relative" ref={containerRef}>
        {session.status === 'connected' ? (
          <div className="w-full h-full flex items-center justify-center bg-gray-900">
            <div className="text-center text-white">
              <Monitor className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg">远程桌面画面</p>
              <p className="text-sm opacity-75 mt-2">
                WebRTC 视频流将在这里显示
              </p>
              {isPaused && (
                <div className="mt-4 px-4 py-2 bg-yellow-600 bg-opacity-80 rounded">
                  <p className="text-sm">画面已暂停</p>
                </div>
              )}
            </div>
          </div>
        ) : session.status === 'connecting' ? (
          <div className="w-full h-full flex items-center justify-center bg-gray-800">
            <div className="text-center text-white">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-lg">正在连接...</p>
              <p className="text-sm opacity-75 mt-2">
                正在连接到 {client.name} ({client.ip})
              </p>
            </div>
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-700">
            <div className="text-center text-white">
              <WifiOff className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg">未连接</p>
              <p className="text-sm opacity-75 mt-2 mb-4">
                与 {client.name} 的连接已断开
              </p>
              <button
                onClick={handleReconnect}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                重新连接
              </button>
            </div>
          </div>
        )}

        {/* 设置面板覆盖层 */}
        {showSettings && session.status === 'connected' && (
          <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-4 w-64">
            <h3 className="text-sm font-medium text-gray-900 mb-3">显示设置</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  画质
                </label>
                <select
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  value={session.quality}
                  onChange={(e) => {
                    setSession(prev => ({
                      ...prev,
                      quality: e.target.value as 'high' | 'medium' | 'low'
                    }))
                  }}
                >
                  <option value="high">高质量</option>
                  <option value="medium">中等质量</option>
                  <option value="low">低质量</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  分辨率
                </label>
                <select
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  value={session.resolution}
                  onChange={(e) => {
                    setSession(prev => ({
                      ...prev,
                      resolution: e.target.value
                    }))
                  }}
                >
                  <option value="1920x1080">1920x1080</option>
                  <option value="1366x768">1366x768</option>
                  <option value="1024x768">1024x768</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

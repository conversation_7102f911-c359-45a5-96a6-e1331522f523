{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'\n    }\n    \n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || loading}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport default Button\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,8OAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface BadgeProps extends HTMLAttributes<HTMLSpanElement> {\n  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info'\n  size?: 'xs' | 'sm' | 'md'\n}\n\nexport default function Badge({ \n  className, \n  variant = 'default', \n  size = 'md', \n  children, \n  ...props \n}: BadgeProps) {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full border'\n  \n  const variants = {\n    default: 'bg-gray-100 text-gray-800 border-gray-200',\n    success: 'bg-green-100 text-green-800 border-green-200',\n    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    danger: 'bg-red-100 text-red-800 border-red-200',\n    info: 'bg-blue-100 text-blue-800 border-blue-200'\n  }\n  \n  const sizes = {\n    xs: 'px-1.5 py-0.5 text-xs',\n    sm: 'px-2 py-0.5 text-xs',\n    md: 'px-2.5 py-1 text-sm'\n  }\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,MAAM,EAC5B,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACQ;IACX,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/remote/ClientSelector.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo } from 'react'\nimport { Monitor, ChevronDown, Wifi, Search, X } from 'lucide-react'\nimport Badge from '@/components/ui/Badge'\n\ninterface Client {\n  id: string\n  name: string\n  ip: string\n  status: 'online' | 'offline'\n  os: string\n  location: string\n  uuid: string\n  owner: {\n    name: string\n    uuid: string\n  }\n  group: string\n}\n\ninterface ClientSelectorProps {\n  selectedClient: Client | null\n  onClientSelect: (client: Client) => void\n}\n\nconst mockClients: Client[] = [\n  {\n    id: 'client-001',\n    name: 'PC-办公室-001',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    location: '北京市 海淀区',\n    uuid: '191215',\n    owner: {\n      name: '张三',\n      uuid: 'a50f8f4a8a6d4c6a2a1f0b6e'\n    },\n    group: '办公'\n  },\n  {\n    id: 'client-002',\n    name: 'PC-会议室-020',\n    ip: '*************',\n    status: 'online',\n    os: 'Ubuntu 18.04',\n    location: '深圳市 南山区',\n    uuid: '220806',\n    owner: {\n      name: '李四',\n      uuid: 'a6d56211bec18f3f43aa'\n    },\n    group: '会议'\n  },\n  {\n    id: 'client-003',\n    name: 'PC-财务-004',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    location: '深圳市 南山区',\n    uuid: '060815',\n    owner: {\n      name: '王五',\n      uuid: 'e4a827f60c4e4a2d6f0b'\n    },\n    group: '财务'\n  },\n  {\n    id: 'client-004',\n    name: 'PC-测试-015',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    location: '深圳市 南山区',\n    uuid: '170901',\n    owner: {\n      name: '赵六',\n      uuid: 'e2a60f4c8e2a6d0f4c6e'\n    },\n    group: '开发'\n  },\n  {\n    id: 'client-005',\n    name: 'PC-开发-008',\n    ip: '*************',\n    status: 'online',\n    os: 'macOS Sonoma',\n    location: '上海市 浦东新区',\n    uuid: '081229',\n    owner: {\n      name: '孙七',\n      uuid: 'f6c4e8a2d6f0b4c8e2a6'\n    },\n    group: '开发'\n  },\n  {\n    id: 'client-006',\n    name: 'PC-设计-012',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    location: '广州市 天河区',\n    uuid: '050613',\n    owner: {\n      name: '周八',\n      uuid: 'a6d56211bec18f3f43bb'\n    },\n    group: '设计'\n  },\n  {\n    id: 'client-007',\n    name: 'PC-销售-003',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 10 Pro',\n    location: '北京市 朝阳区',\n    uuid: '050815',\n    owner: {\n      name: '吴九',\n      uuid: 'c4e8a2d6f0b4c8e2a6d0'\n    },\n    group: '销售'\n  },\n  {\n    id: 'client-008',\n    name: 'PC-运维-009',\n    ip: '*************',\n    status: 'online',\n    os: 'CentOS 8',\n    location: '深圳市 福田区',\n    uuid: '081229',\n    owner: {\n      name: '郑十',\n      uuid: 'f6c4e8a2d6f0b4c8e2a7'\n    },\n    group: '运维'\n  }\n]\n\nexport default function ClientSelector({ selectedClient, onClientSelect }: ClientSelectorProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedGroups, setSelectedGroups] = useState<string[]>([])\n\n  // 只显示在线客户端\n  const onlineClients = mockClients.filter(client => client.status === 'online')\n\n  // 获取所有分组\n  const allGroups = useMemo(() => {\n    const groups = new Set<string>()\n    onlineClients.forEach(client => {\n      groups.add(client.group)\n    })\n    return Array.from(groups).sort()\n  }, [onlineClients])\n\n  // 过滤客户端\n  const filteredClients = useMemo(() => {\n    return onlineClients.filter(client => {\n      // 搜索过滤 - 支持搜索客户端名称、归属用户、UUID\n      const matchesSearch = searchTerm === '' ||\n        client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        client.owner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        client.uuid.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        client.owner.uuid.toLowerCase().includes(searchTerm.toLowerCase())\n\n      // 分组过滤\n      const matchesGroup = selectedGroups.length === 0 ||\n        selectedGroups.includes(client.group)\n\n      return matchesSearch && matchesGroup\n    })\n  }, [onlineClients, searchTerm, selectedGroups])\n\n  const handleClientSelect = (client: Client) => {\n    onClientSelect(client)\n    setIsOpen(false)\n    setSearchTerm('')\n    setSelectedGroups([])\n  }\n\n  const handleGroupToggle = (group: string) => {\n    setSelectedGroups(prev =>\n      prev.includes(group)\n        ? prev.filter(g => g !== group)\n        : [...prev, group]\n    )\n  }\n\n  const clearSearch = () => {\n    setSearchTerm('')\n  }\n\n  const clearFilters = () => {\n    setSearchTerm('')\n    setSelectedGroups([])\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* 选择器按钮 */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center justify-between w-full min-w-64 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n      >\n        <div className=\"flex items-center\">\n          <Monitor className=\"h-5 w-5 text-gray-400 mr-3\" />\n          <div className=\"text-left\">\n            {selectedClient ? (\n              <>\n                <div className=\"text-sm font-medium text-gray-900\">\n                  {selectedClient.name} • {selectedClient.owner.name}\n                </div>\n                <div className=\"text-xs text-gray-500\">\n                  {selectedClient.ip} • {selectedClient.uuid}\n                </div>\n              </>\n            ) : (\n              <div className=\"text-sm text-gray-500\">选择客户端</div>\n            )}\n          </div>\n        </div>\n        <div className=\"flex items-center\">\n          {selectedClient && (\n            <Badge \n              variant={selectedClient.status === 'online' ? 'success' : 'secondary'}\n              size=\"sm\"\n              className=\"mr-2\"\n            >\n              {selectedClient.status === 'online' ? '在线' : '离线'}\n            </Badge>\n          )}\n          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\n        </div>\n      </button>\n\n      {/* 下拉列表 */}\n      {isOpen && (\n        <>\n          {/* 遮罩层 */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n\n          {/* 下拉菜单 */}\n          <div className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-[500px] flex flex-col\">\n            {/* 搜索框 */}\n            <div className=\"p-3 border-b border-gray-200\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索客户端名称、归属用户、UUID...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-8 py-2 border border-gray-300 rounded-md text-sm text-gray-900 placeholder-gray-500 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                {searchTerm && (\n                  <button\n                    onClick={clearSearch}\n                    className=\"absolute right-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded\"\n                  >\n                    <X className=\"h-3 w-3 text-gray-400\" />\n                  </button>\n                )}\n              </div>\n            </div>\n\n            {/* 分组筛选 */}\n            <div className=\"p-3 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-xs font-medium text-gray-700\">分组筛选</span>\n                {selectedGroups.length > 0 && (\n                  <button\n                    onClick={clearFilters}\n                    className=\"text-xs text-blue-600 hover:text-blue-800\"\n                  >\n                    清除筛选\n                  </button>\n                )}\n              </div>\n              <div className=\"flex flex-wrap gap-1\">\n                {allGroups.map(group => (\n                  <button\n                    key={group}\n                    onClick={() => handleGroupToggle(group)}\n                    className={`px-2 py-1 text-xs rounded-full border transition-colors ${\n                      selectedGroups.includes(group)\n                        ? 'bg-blue-100 border-blue-300 text-blue-700'\n                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'\n                    }`}\n                  >\n                    {group}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* 客户端列表 */}\n            <div className=\"flex-1 overflow-y-auto max-h-80\">\n              <div className=\"p-2\">\n                <div className=\"flex items-center justify-between px-2 py-1 mb-2\">\n                  <span className=\"text-xs font-medium text-gray-500\">\n                    在线客户端 ({filteredClients.length})\n                  </span>\n                  {(searchTerm || selectedGroups.length > 0) && (\n                    <span className=\"text-xs text-gray-400\">\n                      共 {onlineClients.length} 台\n                    </span>\n                  )}\n                </div>\n\n                {filteredClients.length > 0 ? (\n                  filteredClients.map((client) => (\n                    <button\n                      key={client.id}\n                      onClick={() => handleClientSelect(client)}\n                      className={`w-full flex items-center px-3 py-2 rounded-md text-left hover:bg-gray-50 transition-colors ${\n                        selectedClient?.id === client.id ? 'bg-blue-50 border border-blue-200' : ''\n                      }`}\n                    >\n                      <div className=\"flex items-center flex-1\">\n                        <Wifi className=\"h-4 w-4 text-green-500 mr-3 flex-shrink-0\" />\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"text-sm font-medium text-gray-900 truncate\">\n                            {client.name} • {client.owner.name}\n                          </div>\n                          <div className=\"text-xs text-gray-500 truncate\">\n                            {client.ip} • {client.uuid}\n                          </div>\n                        </div>\n                        <Badge variant=\"success\" size=\"xs\" className=\"ml-2 flex-shrink-0\">\n                          在线\n                        </Badge>\n                      </div>\n                    </button>\n                  ))\n                ) : (\n                  <div className=\"px-3 py-8 text-center text-gray-500\">\n                    <Monitor className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n                    <p className=\"text-sm\">未找到匹配的客户端</p>\n                    <p className=\"text-xs mt-1\">请尝试调整搜索条件或分组筛选</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AA0BA,MAAM,cAAwB;IAC5B;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;CACD;AAEc,SAAS,eAAe,EAAE,cAAc,EAAE,cAAc,EAAuB;IAC5F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjE,WAAW;IACX,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;IAErE,SAAS;IACT,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,SAAS,IAAI;QACnB,cAAc,OAAO,CAAC,CAAA;YACpB,OAAO,GAAG,CAAC,OAAO,KAAK;QACzB;QACA,OAAO,MAAM,IAAI,CAAC,QAAQ,IAAI;IAChC,GAAG;QAAC;KAAc;IAElB,QAAQ;IACR,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,OAAO,cAAc,MAAM,CAAC,CAAA;YAC1B,6BAA6B;YAC7B,MAAM,gBAAgB,eAAe,MACnC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAEjE,OAAO;YACP,MAAM,eAAe,eAAe,MAAM,KAAK,KAC7C,eAAe,QAAQ,CAAC,OAAO,KAAK;YAEtC,OAAO,iBAAiB;QAC1B;IACF,GAAG;QAAC;QAAe;QAAY;KAAe;IAE9C,MAAM,qBAAqB,CAAC;QAC1B,eAAe;QACf,UAAU;QACV,cAAc;QACd,kBAAkB,EAAE;IACtB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,CAAA,OAChB,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,SACvB;mBAAI;gBAAM;aAAM;IAExB;IAEA,MAAM,cAAc;QAClB,cAAc;IAChB;IAEA,MAAM,eAAe;QACnB,cAAc;QACd,kBAAkB,EAAE;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAI,WAAU;0CACZ,+BACC;;sDACE,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,IAAI;gDAAC;gDAAI,eAAe,KAAK,CAAC,IAAI;;;;;;;sDAEpD,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,EAAE;gDAAC;gDAAI,eAAe,IAAI;;;;;;;;iEAI9C,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAI7C,8OAAC;wBAAI,WAAU;;4BACZ,gCACC,8OAAC,iIAAA,CAAA,UAAK;gCACJ,SAAS,eAAe,MAAM,KAAK,WAAW,YAAY;gCAC1D,MAAK;gCACL,WAAU;0CAET,eAAe,MAAM,KAAK,WAAW,OAAO;;;;;;0CAGjD,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAW,CAAC,2CAA2C,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;;;;;;;YAKrG,wBACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;wCAEX,4BACC,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAOrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;4CACnD,eAAe,MAAM,GAAG,mBACvB,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAKL,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAA,sBACb,8OAAC;gDAEC,SAAS,IAAM,kBAAkB;gDACjC,WAAW,CAAC,wDAAwD,EAClE,eAAe,QAAQ,CAAC,SACpB,8CACA,8DACJ;0DAED;+CARI;;;;;;;;;;;;;;;;0CAeb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAoC;wDAC1C,gBAAgB,MAAM;wDAAC;;;;;;;gDAEhC,CAAC,cAAc,eAAe,MAAM,GAAG,CAAC,mBACvC,8OAAC;oDAAK,WAAU;;wDAAwB;wDACnC,cAAc,MAAM;wDAAC;;;;;;;;;;;;;wCAK7B,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,uBACnB,8OAAC;gDAEC,SAAS,IAAM,mBAAmB;gDAClC,WAAW,CAAC,2FAA2F,EACrG,gBAAgB,OAAO,OAAO,EAAE,GAAG,sCAAsC,IACzE;0DAEF,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEACZ,OAAO,IAAI;wEAAC;wEAAI,OAAO,KAAK,CAAC,IAAI;;;;;;;8EAEpC,8OAAC;oEAAI,WAAU;;wEACZ,OAAO,EAAE;wEAAC;wEAAI,OAAO,IAAI;;;;;;;;;;;;;sEAG9B,8OAAC,iIAAA,CAAA,UAAK;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,WAAU;sEAAqB;;;;;;;;;;;;+CAhB/D,OAAO,EAAE;;;;sEAuBlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAE,WAAU;8DAAU;;;;;;8DACvB,8OAAC;oDAAE,WAAU;8DAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhD", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/app/remote/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport {\n  Maximize,\n  Minimize,\n  Volume2,\n  VolumeX,\n  Settings,\n  Upload,\n  Download,\n  Terminal,\n  Clipboard,\n  Power,\n  RotateCcw,\n  Monitor,\n  Wifi,\n  WifiOff\n} from 'lucide-react'\nimport Button from '@/components/ui/Button'\nimport Badge from '@/components/ui/Badge'\nimport ClientSelector from '@/components/remote/ClientSelector'\nimport { useLog } from '@/contexts/LogContext'\n\ninterface RemoteSession {\n  clientId: string\n  clientName: string\n  clientIP: string\n  status: 'connecting' | 'connected' | 'disconnected'\n  quality: 'high' | 'medium' | 'low'\n  resolution: string\n  startTime: string\n}\n\ninterface Client {\n  id: string\n  name: string\n  ip: string\n  status: 'online' | 'offline'\n  os: string\n  location: string\n  uuid: string\n  owner: {\n    name: string\n    uuid: string\n  }\n  group: string\n}\n\nexport default function RemotePage() {\n  const { addLog } = useLog()\n\n  const [selectedClient, setSelectedClient] = useState<Client | null>({\n    id: 'client-001',\n    name: 'PC-办公室-001',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    location: '北京市 海淀区',\n    uuid: '191215',\n    owner: {\n      name: '张三',\n      uuid: 'a50f8f4a8a6d4c6a2a1f0b6e'\n    },\n    group: '办公'\n  })\n\n  const [session, setSession] = useState<RemoteSession>({\n    clientId: 'client-001',\n    clientName: 'PC-办公室-001',\n    clientIP: '*************',\n    status: 'connected',\n    quality: 'high',\n    resolution: '1920x1080',\n    startTime: '2024-01-15 14:30:00'\n  })\n\n  const [isFullscreen, setIsFullscreen] = useState(false)\n  const [isMuted, setIsMuted] = useState(false)\n  const [isControlEnabled, setIsControlEnabled] = useState(true)\n  const [showSettings, setShowSettings] = useState(false)\n  \n  const videoRef = useRef<HTMLVideoElement>(null)\n  const containerRef = useRef<HTMLDivElement>(null)\n\n  const handleFullscreen = () => {\n    if (!isFullscreen) {\n      containerRef.current?.requestFullscreen()\n    } else {\n      document.exitFullscreen()\n    }\n    setIsFullscreen(!isFullscreen)\n  }\n\n  const handleClientSelect = (client: Client) => {\n    setSelectedClient(client)\n    if (client.status === 'online') {\n      setSession(prev => ({\n        ...prev,\n        clientId: client.id,\n        clientName: client.name,\n        clientIP: client.ip,\n        status: 'connecting'\n      }))\n      addLog(`正在连接到 ${client.name}...`, 'info', 'remote')\n\n      // 模拟连接过程\n      setTimeout(() => {\n        setSession(prev => ({ ...prev, status: 'connected' }))\n        addLog(`已连接到 ${client.name}`, 'success', 'remote')\n      }, 1500)\n    } else {\n      addLog(`客户端 ${client.name} 离线，无法连接`, 'warning', 'remote')\n    }\n  }\n\n  const handleDisconnect = () => {\n    setSession(prev => ({ ...prev, status: 'disconnected' }))\n    addLog(`与 ${session.clientName} 的连接已断开`, 'warning', 'remote')\n  }\n\n  const handleReconnect = () => {\n    setSession(prev => ({ ...prev, status: 'connecting' }))\n    addLog(`正在重新连接到 ${session.clientName}...`, 'info', 'remote')\n\n    setTimeout(() => {\n      setSession(prev => ({ ...prev, status: 'connected' }))\n      addLog(`重新连接到 ${session.clientName} 成功`, 'success', 'remote')\n    }, 2000)\n  }\n\n  const getStatusBadge = () => {\n    switch (session.status) {\n      case 'connected':\n        return <Badge variant=\"success\">已连接</Badge>\n      case 'connecting':\n        return <Badge variant=\"warning\">连接中</Badge>\n      case 'disconnected':\n        return <Badge variant=\"danger\">已断开</Badge>\n    }\n  }\n\n  return (\n    <div className=\"h-[calc(100vh-8rem)] flex flex-col space-y-4\">\n      {/* 顶部控制栏 */}\n      <div className=\"bg-white rounded-lg shadow p-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* 左侧：客户端选择器 */}\n          <div className=\"flex items-center space-x-4\">\n            <ClientSelector\n              selectedClient={selectedClient}\n              onClientSelect={handleClientSelect}\n            />\n            {getStatusBadge()}\n          </div>\n\n          {/* 中间：连接信息 */}\n          {session.status === 'connected' && (\n            <div className=\"flex items-center space-x-6 text-sm text-gray-600\">\n              <span>分辨率: {session.resolution}</span>\n              <span>质量: {session.quality.toUpperCase()}</span>\n              <span>延迟: 45ms</span>\n              <span>帧率: 60fps</span>\n            </div>\n          )}\n\n          {/* 右侧：控制按钮 */}\n          <div className=\"flex items-center space-x-2\">\n            {/* 快捷操作按钮 */}\n            <Button variant=\"outline\" size=\"sm\">\n              <Upload className=\"h-4 w-4 mr-1\" />\n              上传\n            </Button>\n            <Button variant=\"outline\" size=\"sm\">\n              <Download className=\"h-4 w-4 mr-1\" />\n              下载\n            </Button>\n            <Button variant=\"outline\" size=\"sm\">\n              <Terminal className=\"h-4 w-4 mr-1\" />\n              终端\n            </Button>\n            <Button variant=\"outline\" size=\"sm\">\n              <Clipboard className=\"h-4 w-4 mr-1\" />\n              剪贴板\n            </Button>\n\n            {/* 分隔线 */}\n            <div className=\"h-6 w-px bg-gray-300 mx-2\"></div>\n\n            {/* 控制按钮 */}\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setIsControlEnabled(!isControlEnabled)}\n            >\n              {isControlEnabled ? (\n                <>\n                  <Wifi className=\"h-4 w-4 mr-1\" />\n                  控制\n                </>\n              ) : (\n                <>\n                  <WifiOff className=\"h-4 w-4 mr-1\" />\n                  控制\n                </>\n              )}\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setIsMuted(!isMuted)}\n            >\n              {isMuted ? <VolumeX className=\"h-4 w-4\" /> : <Volume2 className=\"h-4 w-4\" />}\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setShowSettings(!showSettings)}\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleFullscreen}\n            >\n              {isFullscreen ? <Minimize className=\"h-4 w-4\" /> : <Maximize className=\"h-4 w-4\" />}\n            </Button>\n\n            {session.status === 'connected' ? (\n              <Button variant=\"danger\" size=\"sm\" onClick={handleDisconnect}>\n                <Power className=\"h-4 w-4 mr-1\" />\n                断开\n              </Button>\n            ) : (\n              <Button variant=\"primary\" size=\"sm\" onClick={handleReconnect}>\n                <RotateCcw className=\"h-4 w-4 mr-1\" />\n                连接\n              </Button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* 主视频区域 - 铺满整个剩余空间 */}\n      <div className=\"flex-1 bg-black rounded-lg overflow-hidden relative\" ref={containerRef}>\n        {session.status === 'connected' ? (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-900\">\n            <div className=\"text-center text-white\">\n              <Monitor className=\"h-16 w-16 mx-auto mb-4 opacity-50\" />\n              <p className=\"text-lg\">远程桌面画面</p>\n              <p className=\"text-sm opacity-75 mt-2\">\n                WebRTC 视频流将在这里显示\n              </p>\n            </div>\n          </div>\n        ) : session.status === 'connecting' ? (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-800\">\n            <div className=\"text-center text-white\">\n              <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4\"></div>\n              <p className=\"text-lg\">正在连接...</p>\n              <p className=\"text-sm opacity-75 mt-2\">\n                正在连接到 {session.clientName}\n              </p>\n            </div>\n          </div>\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-700\">\n            <div className=\"text-center text-white\">\n              <WifiOff className=\"h-16 w-16 mx-auto mb-4 opacity-50\" />\n              <p className=\"text-lg\">未连接</p>\n              <p className=\"text-sm opacity-75 mt-2 mb-4\">\n                请选择一个在线客户端进行连接\n              </p>\n              {selectedClient?.status === 'online' && (\n                <Button variant=\"primary\" onClick={handleReconnect}>\n                  连接到 {selectedClient.name}\n                </Button>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* 设置面板覆盖层 */}\n        {showSettings && session.status === 'connected' && (\n          <div className=\"absolute top-4 right-4 bg-white rounded-lg shadow-lg p-4 w-64\">\n            <h3 className=\"text-sm font-medium text-gray-900 mb-3\">显示设置</h3>\n            <div className=\"space-y-3\">\n              <div>\n                <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                  画质\n                </label>\n                <select\n                  className=\"w-full text-sm border border-gray-300 rounded px-2 py-1 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                  value={session.quality}\n                  onChange={(e) => {\n                    setSession(prev => ({\n                      ...prev,\n                      quality: e.target.value as 'high' | 'medium' | 'low'\n                    }))\n                    addLog(`画质已调整为 ${e.target.value}`, 'info', 'remote')\n                  }}\n                >\n                  <option value=\"high\">高质量</option>\n                  <option value=\"medium\">中等质量</option>\n                  <option value=\"low\">低质量</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                  分辨率\n                </label>\n                <select\n                  className=\"w-full text-sm border border-gray-300 rounded px-2 py-1 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                  value={session.resolution}\n                  onChange={(e) => {\n                    setSession(prev => ({\n                      ...prev,\n                      resolution: e.target.value\n                    }))\n                    addLog(`分辨率已调整为 ${e.target.value}`, 'info', 'remote')\n                  }}\n                >\n                  <option value=\"1920x1080\">1920x1080</option>\n                  <option value=\"1366x768\">1366x768</option>\n                  <option value=\"1024x768\">1024x768</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AAtBA;;;;;;;;AAiDe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,SAAM,AAAD;IAExB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAClE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,UAAU;QACV,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,WAAW;IACb;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,MAAM,mBAAmB;QACvB,IAAI,CAAC,cAAc;YACjB,aAAa,OAAO,EAAE;QACxB,OAAO;YACL,SAAS,cAAc;QACzB;QACA,gBAAgB,CAAC;IACnB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,IAAI,OAAO,MAAM,KAAK,UAAU;YAC9B,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,UAAU,OAAO,EAAE;oBACnB,YAAY,OAAO,IAAI;oBACvB,UAAU,OAAO,EAAE;oBACnB,QAAQ;gBACV,CAAC;YACD,OAAO,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ;YAE1C,SAAS;YACT,WAAW;gBACT,WAAW,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAY,CAAC;gBACpD,OAAO,CAAC,KAAK,EAAE,OAAO,IAAI,EAAE,EAAE,WAAW;YAC3C,GAAG;QACL,OAAO;YACL,OAAO,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,WAAW;QAClD;IACF;IAEA,MAAM,mBAAmB;QACvB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;YAAe,CAAC;QACvD,OAAO,CAAC,EAAE,EAAE,QAAQ,UAAU,CAAC,OAAO,CAAC,EAAE,WAAW;IACtD;IAEA,MAAM,kBAAkB;QACtB,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;YAAa,CAAC;QACrD,OAAO,CAAC,QAAQ,EAAE,QAAQ,UAAU,CAAC,GAAG,CAAC,EAAE,QAAQ;QAEnD,WAAW;YACT,WAAW,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAY,CAAC;YACpD,OAAO,CAAC,MAAM,EAAE,QAAQ,UAAU,CAAC,GAAG,CAAC,EAAE,WAAW;QACtD,GAAG;IACL;IAEA,MAAM,iBAAiB;QACrB,OAAQ,QAAQ,MAAM;YACpB,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,UAAK;oBAAC,SAAQ;8BAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,UAAK;oBAAC,SAAQ;8BAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,UAAK;oBAAC,SAAQ;8BAAS;;;;;;QACnC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,UAAc;oCACb,gBAAgB;oCAChB,gBAAgB;;;;;;gCAEjB;;;;;;;wBAIF,QAAQ,MAAM,KAAK,6BAClB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAK;wCAAM,QAAQ,UAAU;;;;;;;8CAC9B,8OAAC;;wCAAK;wCAAK,QAAQ,OAAO,CAAC,WAAW;;;;;;;8CACtC,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAK;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAKxC,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,oBAAoB,CAAC;8CAEnC,iCACC;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;qEAInC;;0DACE,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;8CAM1C,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,WAAW,CAAC;8CAE1B,wBAAU,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAAe,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAGlE,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,gBAAgB,CAAC;8CAEhC,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAGtB,8OAAC,kIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;8CAER,6BAAe,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;6DAAe,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;gCAGxE,QAAQ,MAAM,KAAK,4BAClB,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAS,MAAK;oCAAK,SAAS;;sDAC1C,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;yDAIpC,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS;;sDAC3C,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAShD,8OAAC;gBAAI,WAAU;gBAAsD,KAAK;;oBACvE,QAAQ,MAAM,KAAK,4BAClB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;;;;;+BAKzC,QAAQ,MAAM,KAAK,6BACrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;;wCAA0B;wCAC9B,QAAQ,UAAU;;;;;;;;;;;;;;;;;6CAK/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CAA+B;;;;;;gCAG3C,gBAAgB,WAAW,0BAC1B,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,SAAS;;wCAAiB;wCAC7C,eAAe,IAAI;;;;;;;;;;;;;;;;;;oBAQjC,gBAAgB,QAAQ,MAAM,KAAK,6BAClC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,WAAU;gDACV,OAAO,QAAQ,OAAO;gDACtB,UAAU,CAAC;oDACT,WAAW,CAAA,OAAQ,CAAC;4DAClB,GAAG,IAAI;4DACP,SAAS,EAAE,MAAM,CAAC,KAAK;wDACzB,CAAC;oDACD,OAAO,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,EAAE,QAAQ;gDAC7C;;kEAEA,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;kDAGxB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,WAAU;gDACV,OAAO,QAAQ,UAAU;gDACzB,UAAU,CAAC;oDACT,WAAW,CAAA,OAAQ,CAAC;4DAClB,GAAG,IAAI;4DACP,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC5B,CAAC;oDACD,OAAO,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,EAAE,QAAQ;gDAC9C;;kEAEA,8OAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C", "debugId": null}}]}
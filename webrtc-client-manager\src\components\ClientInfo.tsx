'use client'

import { 
  Monitor, 
  Wifi, 
  WifiOff, 
  User, 
  MapPin, 
  Clock,
  Cpu,
  HardDrive,
  MemoryStick,
  Network,
  Info,
  Activity
} from 'lucide-react'
import { Client } from '@/types/client'

interface ClientInfoProps {
  client: Client
}

export default function ClientInfo({ client }: ClientInfoProps) {
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const formatSpeed = (bytesPerSec: number) => {
    return formatBytes(bytesPerSec) + '/s'
  }

  const getStatusColor = (status: string) => {
    return status === 'online' ? 'text-green-600' : 'text-gray-400'
  }

  const getUsageColor = (usage: number) => {
    if (usage >= 80) return 'text-red-600'
    if (usage >= 60) return 'text-yellow-600'
    return 'text-green-600'
  }

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* 左侧：基本信息 */}
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${client.status === 'online' ? 'bg-green-100' : 'bg-gray-100'}`}>
              {client.status === 'online' ? (
                <Wifi className="h-5 w-5 text-green-600" />
              ) : (
                <WifiOff className="h-5 w-5 text-gray-400" />
              )}
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">{client.name}</h2>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Monitor className="h-3 w-3" />
                  <span>{client.ip}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3" />
                  <span>{client.owner.name}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MapPin className="h-3 w-3" />
                  <span>{client.location}</span>
                </div>
              </div>
            </div>
          </div>

          {/* 状态标签 */}
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${
            client.status === 'online' 
              ? 'bg-green-100 text-green-700' 
              : 'bg-gray-100 text-gray-500'
          }`}>
            {client.status === 'online' ? '在线' : '离线'}
          </div>
        </div>

        {/* 右侧：系统信息 */}
        {client.status === 'online' && (
          <div className="flex items-center space-x-6">
            {/* CPU使用率 */}
            <div className="text-center">
              <div className="flex items-center space-x-1 mb-1">
                <Cpu className="h-4 w-4 text-gray-400" />
                <span className="text-xs text-gray-500">CPU</span>
              </div>
              <div className={`text-sm font-medium ${getUsageColor(client.cpu.usage)}`}>
                {client.cpu.usage}%
              </div>
            </div>

            {/* 内存使用率 */}
            <div className="text-center">
              <div className="flex items-center space-x-1 mb-1">
                <MemoryStick className="h-4 w-4 text-gray-400" />
                <span className="text-xs text-gray-500">内存</span>
              </div>
              <div className={`text-sm font-medium ${getUsageColor(client.memory.usage)}`}>
                {client.memory.usage}%
              </div>
              <div className="text-xs text-gray-400">
                {formatBytes(client.memory.available)} / {formatBytes(client.memory.total)}
              </div>
            </div>

            {/* 磁盘使用率 */}
            <div className="text-center">
              <div className="flex items-center space-x-1 mb-1">
                <HardDrive className="h-4 w-4 text-gray-400" />
                <span className="text-xs text-gray-500">磁盘</span>
              </div>
              <div className={`text-sm font-medium ${getUsageColor(client.disk.usage)}`}>
                {client.disk.usage}%
              </div>
              <div className="text-xs text-gray-400">
                {formatBytes(client.disk.available)} 可用
              </div>
            </div>

            {/* 网络速度 */}
            <div className="text-center">
              <div className="flex items-center space-x-1 mb-1">
                <Network className="h-4 w-4 text-gray-400" />
                <span className="text-xs text-gray-500">网络</span>
              </div>
              <div className="text-sm font-medium text-blue-600">
                ↑ {formatSpeed(client.network.upload)}
              </div>
              <div className="text-sm font-medium text-green-600">
                ↓ {formatSpeed(client.network.download)}
              </div>
            </div>

            {/* 系统信息 */}
            <div className="text-center">
              <div className="flex items-center space-x-1 mb-1">
                <Info className="h-4 w-4 text-gray-400" />
                <span className="text-xs text-gray-500">系统</span>
              </div>
              <div className="text-sm font-medium text-gray-700">
                {client.os}
              </div>
              <div className="text-xs text-gray-400">
                版本 {client.version}
              </div>
            </div>

            {/* 最后在线时间 */}
            <div className="text-center">
              <div className="flex items-center space-x-1 mb-1">
                <Clock className="h-4 w-4 text-gray-400" />
                <span className="text-xs text-gray-500">最后在线</span>
              </div>
              <div className="text-sm font-medium text-gray-700">
                {client.lastSeen}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 额外信息行 */}
      <div className="mt-3 flex items-center justify-between text-sm">
        <div className="flex items-center space-x-4 text-gray-500">
          <span>UUID: {client.uuid}</span>
          <span>分组: {client.group}</span>
          {client.status === 'online' && (
            <span>CPU型号: {client.cpu.model}</span>
          )}
        </div>
        
        {client.status === 'online' && (
          <div className="flex items-center space-x-2 text-gray-500">
            <Activity className="h-4 w-4" />
            <span>实时监控中</span>
          </div>
        )}
      </div>
    </div>
  )
}

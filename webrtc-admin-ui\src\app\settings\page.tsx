'use client'

import { useState } from 'react'
import { 
  Save, 
  RefreshCw, 
  Settings, 
  Shield, 
  Monitor, 
  Wifi,
  Lock,
  Key,
  Clock,
  Users,
  AlertTriangle
} from 'lucide-react'
import { SystemSettings } from '@/types'
import Button from '@/components/ui/Button'

// 模拟系统设置数据
const mockSettings: SystemSettings = {
  webrtc: {
    stunServers: ['stun:stun.l.google.com:19302', 'stun:stun1.l.google.com:19302'],
    turnServers: [
      {
        urls: 'turn:turn.example.com:3478',
        username: 'turnuser',
        credential: 'turnpass'
      }
    ]
  },
  security: {
    authMethod: 'both',
    sessionTimeout: 3600,
    maxConcurrentSessions: 5
  },
  display: {
    defaultResolution: '1920x1080',
    defaultQuality: 'high',
    enableAudio: true
  }
}

export default function SettingsPage() {
  const [settings, setSettings] = useState<SystemSettings>(mockSettings)
  const [activeTab, setActiveTab] = useState('webrtc')
  const [hasChanges, setHasChanges] = useState(false)

  const tabs = [
    { id: 'webrtc', label: 'WebRTC 配置', icon: Wifi },
    { id: 'security', label: '安全设置', icon: Shield },
    { id: 'display', label: '显示设置', icon: Monitor },
    { id: 'system', label: '系统设置', icon: Settings }
  ]

  const handleSave = () => {
    console.log('保存设置:', settings)
    setHasChanges(false)
    // TODO: 实现保存逻辑
  }

  const handleReset = () => {
    setSettings(mockSettings)
    setHasChanges(false)
  }

  const updateSettings = (section: keyof SystemSettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
    setHasChanges(true)
  }

  const addStunServer = () => {
    const newServer = prompt('请输入 STUN 服务器地址:')
    if (newServer) {
      setSettings(prev => ({
        ...prev,
        webrtc: {
          ...prev.webrtc,
          stunServers: [...prev.webrtc.stunServers, newServer]
        }
      }))
      setHasChanges(true)
    }
  }

  const removeStunServer = (index: number) => {
    setSettings(prev => ({
      ...prev,
      webrtc: {
        ...prev.webrtc,
        stunServers: prev.webrtc.stunServers.filter((_, i) => i !== index)
      }
    }))
    setHasChanges(true)
  }

  const addTurnServer = () => {
    setSettings(prev => ({
      ...prev,
      webrtc: {
        ...prev.webrtc,
        turnServers: [...prev.webrtc.turnServers, {
          urls: '',
          username: '',
          credential: ''
        }]
      }
    }))
    setHasChanges(true)
  }

  const updateTurnServer = (index: number, field: string, value: string) => {
    setSettings(prev => ({
      ...prev,
      webrtc: {
        ...prev.webrtc,
        turnServers: prev.webrtc.turnServers.map((server, i) => 
          i === index ? { ...server, [field]: value } : server
        )
      }
    }))
    setHasChanges(true)
  }

  const removeTurnServer = (index: number) => {
    setSettings(prev => ({
      ...prev,
      webrtc: {
        ...prev.webrtc,
        turnServers: prev.webrtc.turnServers.filter((_, i) => i !== index)
      }
    }))
    setHasChanges(true)
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">系统设置</h1>
            <p className="text-sm text-gray-500 mt-1">配置系统参数和安全选项</p>
          </div>
          <div className="flex items-center space-x-2">
            {hasChanges && (
              <div className="flex items-center text-yellow-600 text-sm mr-4">
                <AlertTriangle className="h-4 w-4 mr-1" />
                有未保存的更改
              </div>
            )}
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RefreshCw className="h-4 w-4 mr-2" />
              重置
            </Button>
            <Button size="sm" onClick={handleSave} disabled={!hasChanges}>
              <Save className="h-4 w-4 mr-2" />
              保存设置
            </Button>
          </div>
        </div>
      </div>

      <div className="flex space-x-6">
        {/* 左侧标签页 */}
        <div className="w-64">
          <nav className="space-y-1">
            {tabs.map(tab => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        {/* 右侧设置内容 */}
        <div className="flex-1">
          {/* WebRTC 配置 */}
          {activeTab === 'webrtc' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">WebRTC 配置</h2>
              
              {/* STUN 服务器 */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-900">STUN 服务器</h3>
                  <Button size="sm" variant="outline" onClick={addStunServer}>
                    添加服务器
                  </Button>
                </div>
                <div className="space-y-2">
                  {settings.webrtc.stunServers.map((server, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={server}
                        onChange={(e) => {
                          const newServers = [...settings.webrtc.stunServers]
                          newServers[index] = e.target.value
                          updateSettings('webrtc', 'stunServers', newServers)
                        }}
                        className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                      />
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => removeStunServer(index)}
                      >
                        删除
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* TURN 服务器 */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-900">TURN 服务器</h3>
                  <Button size="sm" variant="outline" onClick={addTurnServer}>
                    添加服务器
                  </Button>
                </div>
                <div className="space-y-4">
                  {settings.webrtc.turnServers.map((server, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            服务器地址
                          </label>
                          <input
                            type="text"
                            value={server.urls}
                            onChange={(e) => updateTurnServer(index, 'urls', e.target.value)}
                            placeholder="turn:example.com:3478"
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            用户名
                          </label>
                          <input
                            type="text"
                            value={server.username}
                            onChange={(e) => updateTurnServer(index, 'username', e.target.value)}
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            密码
                          </label>
                          <div className="flex space-x-2">
                            <input
                              type="password"
                              value={server.credential}
                              onChange={(e) => updateTurnServer(index, 'credential', e.target.value)}
                              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                            />
                            <Button 
                              size="sm" 
                              variant="outline" 
                              onClick={() => removeTurnServer(index)}
                            >
                              删除
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 安全设置 */}
          {activeTab === 'security' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">安全设置</h2>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Lock className="h-4 w-4 inline mr-2" />
                    认证方式
                  </label>
                  <select
                    value={settings.security.authMethod}
                    onChange={(e) => updateSettings('security', 'authMethod', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  >
                    <option value="password">密码认证</option>
                    <option value="key">密钥认证</option>
                    <option value="both">密码 + 密钥</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Clock className="h-4 w-4 inline mr-2" />
                    会话超时时间 (秒)
                  </label>
                  <input
                    type="number"
                    value={settings.security.sessionTimeout}
                    onChange={(e) => updateSettings('security', 'sessionTimeout', parseInt(e.target.value))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Users className="h-4 w-4 inline mr-2" />
                    最大并发会话数
                  </label>
                  <input
                    type="number"
                    value={settings.security.maxConcurrentSessions}
                    onChange={(e) => updateSettings('security', 'maxConcurrentSessions', parseInt(e.target.value))}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  />
                </div>
              </div>
            </div>
          )}

          {/* 显示设置 */}
          {activeTab === 'display' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">显示设置</h2>
              
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    默认分辨率
                  </label>
                  <select
                    value={settings.display.defaultResolution}
                    onChange={(e) => updateSettings('display', 'defaultResolution', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  >
                    <option value="1920x1080">1920x1080 (Full HD)</option>
                    <option value="1366x768">1366x768 (HD)</option>
                    <option value="1024x768">1024x768 (XGA)</option>
                    <option value="800x600">800x600 (SVGA)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    默认画质
                  </label>
                  <select
                    value={settings.display.defaultQuality}
                    onChange={(e) => updateSettings('display', 'defaultQuality', e.target.value)}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  >
                    <option value="high">高质量</option>
                    <option value="medium">中等质量</option>
                    <option value="low">低质量</option>
                  </select>
                </div>

                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.display.enableAudio}
                      onChange={(e) => updateSettings('display', 'enableAudio', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">启用音频传输</span>
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* 系统设置 */}
          {activeTab === 'system' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-6">系统设置</h2>
              
              <div className="space-y-6">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                    <span className="text-sm font-medium text-yellow-800">
                      系统维护功能
                    </span>
                  </div>
                  <p className="text-sm text-yellow-700 mt-1">
                    这些操作可能会影响系统运行，请谨慎操作。
                  </p>
                </div>

                <div className="space-y-4">
                  <Button variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    重启系统服务
                  </Button>
                  
                  <Button variant="outline">
                    <Save className="h-4 w-4 mr-2" />
                    备份系统配置
                  </Button>
                  
                  <Button variant="outline">
                    <Key className="h-4 w-4 mr-2" />
                    重新生成密钥
                  </Button>
                  
                  <Button variant="danger">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    恢复出厂设置
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

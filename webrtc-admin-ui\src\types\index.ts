export interface Client {
  id: string
  name: string
  ip: string
  status: 'online' | 'offline' | 'connecting'
  os: string
  version: string
  lastSeen: string
  uptime: number
  tags: string[]
  group: string
  cpu: number
  memory: number
  disk: number
  network: {
    upload: number
    download: number
  }
}

export interface SystemInfo {
  cpu: {
    usage: number
    cores: number
    model: string
  }
  memory: {
    total: number
    used: number
    available: number
  }
  disk: {
    total: number
    used: number
    available: number
  }
  network: {
    upload: number
    download: number
  }
  uptime: number
}

export interface LogEntry {
  id: string
  timestamp: string
  operator: string
  target: string
  action: string
  result: 'success' | 'failed' | 'pending'
  details?: string
}

export interface User {
  id: string
  username: string
  role: 'admin' | 'operator' | 'viewer'
  lastLogin: string
  loginIP: string
  isActive: boolean
  permissions: string[]
}

export interface RemoteSession {
  id: string
  clientId: string
  clientName: string
  startTime: string
  duration: number
  status: 'active' | 'ended'
  quality: 'high' | 'medium' | 'low'
  resolution: string
  operator: string
}

export interface SystemSettings {
  webrtc: {
    stunServers: string[]
    turnServers: Array<{
      urls: string
      username: string
      credential: string
    }>
  }
  security: {
    authMethod: 'password' | 'key' | 'both'
    sessionTimeout: number
    maxConcurrentSessions: number
  }
  display: {
    defaultResolution: string
    defaultQuality: 'high' | 'medium' | 'low'
    enableAudio: boolean
  }
}

export interface NavigationItem {
  id: string
  label: string
  icon: string
  href: string
  badge?: number
  children?: NavigationItem[]
}

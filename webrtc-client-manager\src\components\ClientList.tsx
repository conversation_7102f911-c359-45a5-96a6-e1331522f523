'use client'

import { useState } from 'react'
import { 
  Search, 
  Monitor, 
  Wifi, 
  WifiOff, 
  User, 
  MapPin, 
  Clock,
  Cpu,
  HardDrive,
  MemoryStick
} from 'lucide-react'
import { Client } from '@/types/client'

interface ClientListProps {
  clients: Client[]
  selectedClient: Client | null
  onClientSelect: (client: Client) => void
}

export default function ClientList({ clients, selectedClient, onClientSelect }: ClientListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'online' | 'offline'>('all')

  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.ip.includes(searchTerm) ||
                         client.owner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.uuid.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = filterStatus === 'all' || client.status === filterStatus
    
    return matchesSearch && matchesStatus
  })

  const onlineCount = clients.filter(c => c.status === 'online').length
  const offlineCount = clients.filter(c => c.status === 'offline').length

  const getStatusIcon = (status: string) => {
    return status === 'online' ? (
      <Wifi className="h-3 w-3 text-green-500" />
    ) : (
      <WifiOff className="h-3 w-3 text-gray-400" />
    )
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">客户端列表</h2>
        
        {/* 搜索框 */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索客户端..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* 状态过滤 */}
        <div className="flex space-x-1">
          <button
            onClick={() => setFilterStatus('all')}
            className={`px-3 py-1 text-xs rounded-full ${
              filterStatus === 'all' 
                ? 'bg-blue-100 text-blue-700' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            全部 ({clients.length})
          </button>
          <button
            onClick={() => setFilterStatus('online')}
            className={`px-3 py-1 text-xs rounded-full ${
              filterStatus === 'online' 
                ? 'bg-green-100 text-green-700' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            在线 ({onlineCount})
          </button>
          <button
            onClick={() => setFilterStatus('offline')}
            className={`px-3 py-1 text-xs rounded-full ${
              filterStatus === 'offline' 
                ? 'bg-gray-100 text-gray-700' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            离线 ({offlineCount})
          </button>
        </div>
      </div>

      {/* 客户端列表 */}
      <div className="flex-1 overflow-y-auto">
        {filteredClients.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <Monitor className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">没有找到匹配的客户端</p>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {filteredClients.map((client) => (
              <div
                key={client.id}
                onClick={() => onClientSelect(client)}
                className={`relative p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                  selectedClient?.id === client.id
                    ? 'client-item-selected'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                {/* 客户端基本信息 */}
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(client.status)}
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {client.name}
                      </h3>
                    </div>
                    <div className="flex items-center space-x-1 mt-1">
                      <User className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">{client.owner.name}</span>
                    </div>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    client.status === 'online' 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-gray-100 text-gray-500'
                  }`}>
                    {client.status === 'online' ? '在线' : '离线'}
                  </div>
                </div>

                {/* 详细信息 */}
                <div className="space-y-1 text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Monitor className="h-3 w-3" />
                    <span>{client.ip}</span>
                    <span>•</span>
                    <span>{client.os}</span>
                  </div>
                  
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-3 w-3" />
                    <span>{client.location}</span>
                  </div>

                  {client.status === 'online' && (
                    <>
                      <div className="flex items-center space-x-3 mt-2">
                        <div className="flex items-center space-x-1">
                          <Cpu className="h-3 w-3" />
                          <span>CPU {client.cpu.usage}%</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MemoryStick className="h-3 w-3" />
                          <span>内存 {client.memory.usage}%</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <HardDrive className="h-3 w-3" />
                        <span>磁盘 {formatBytes(client.disk.available)} 可用</span>
                      </div>
                    </>
                  )}

                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>最后在线: {client.lastSeen}</span>
                  </div>
                </div>

                {/* 分组标签 */}
                <div className="mt-2">
                  <span className="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                    {client.group}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

'use client'

import { useState, useRef, useEffect } from 'react'
import { 
  Terminal, 
  Send, 
  Trash2, 
  Copy,
  Download,
  Settings,
  Play,
  Square,
  RotateCcw,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import ClientSelector from '@/components/remote/ClientSelector'
import Badge from '@/components/ui/Badge'

interface Client {
  id: string
  name: string
  ip: string
  status: 'online' | 'offline'
  os: string
  location: string
  uuid: string
  owner: {
    name: string
    uuid: string
  }
  group: string
}

interface CommandHistory {
  id: string
  command: string
  output: string
  timestamp: string
  status: 'success' | 'error' | 'running'
  duration?: number
}

const mockCommandHistory: CommandHistory[] = [
  {
    id: '1',
    command: 'dir',
    output: `驱动器 C 中的卷没有标签。
卷的序列号是 1234-5678

C:\\Users\\<USER>\\Windows
系统目录:         C:\\Windows\\system32
启动设备:         \\Device\\HarddiskVolume2
系统区域设置:     zh-cn;中文(中国)
输入法区域设置:   zh-cn;中文(中国)
时区:             (UTC+08:00) 北京，重庆，香港特别行政区，乌鲁木齐
物理内存总量:     16,384 MB
可用的物理内存:   8,192 MB
虚拟内存: 最大值: 32,768 MB
虚拟内存: 可用:   16,384 MB
虚拟内存: 使用中: 16,384 MB`,
    timestamp: '2024-01-15 14:25:10',
    status: 'success',
    duration: 2.3
  },
  {
    id: '3',
    command: 'ping google.com',
    output: `正在 Ping google.com [142.250.191.14] 具有 32 字节的数据:
来自 142.250.191.14 的回复: 字节=32 时间=15ms TTL=117
来自 142.250.191.14 的回复: 字节=32 时间=14ms TTL=117
来自 142.250.191.14 的回复: 字节=32 时间=16ms TTL=117
来自 142.250.191.14 的回复: 字节=32 时间=15ms TTL=117

142.250.191.14 的 Ping 统计信息:
    数据包: 已发送 = 4，已接收 = 4，丢失 = 0 (0% 丢失)，
往返行程的估计时间(以毫秒为单位):
    最短 = 14ms，最长 = 16ms，平均 = 15ms`,
    timestamp: '2024-01-15 14:20:45',
    status: 'success',
    duration: 4.1
  }
]

export default function CmdPage() {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [currentCommand, setCurrentCommand] = useState('')
  const [commandHistory, setCommandHistory] = useState<CommandHistory[]>(mockCommandHistory)
  const [isRunning, setIsRunning] = useState(false)
  const terminalRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [commandHistory])

  const executeCommand = () => {
    if (!currentCommand.trim() || !selectedClient || isRunning) return

    const newCommand: CommandHistory = {
      id: Date.now().toString(),
      command: currentCommand,
      output: '',
      timestamp: new Date().toLocaleString('zh-CN'),
      status: 'running'
    }

    setCommandHistory(prev => [...prev, newCommand])
    setIsRunning(true)
    setCurrentCommand('')

    // 模拟命令执行
    setTimeout(() => {
      const mockOutput = `执行命令: ${currentCommand}
输出结果...
命令执行完成。`

      setCommandHistory(prev => 
        prev.map(cmd => 
          cmd.id === newCommand.id 
            ? { ...cmd, output: mockOutput, status: 'success', duration: Math.random() * 3 + 0.5 }
            : cmd
        )
      )
      setIsRunning(false)
    }, 1000 + Math.random() * 2000)
  }

  const clearHistory = () => {
    setCommandHistory([])
  }

  const copyOutput = (output: string) => {
    navigator.clipboard.writeText(output)
  }

  const getStatusIcon = (status: CommandHistory['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      executeCommand()
    }
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 顶部工具栏 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900">CMD命令终端</h1>
          <div className="flex items-center space-x-2">
            <button 
              onClick={clearHistory}
              className="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              清空历史
            </button>
            <button className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center">
              <Download className="h-4 w-4 mr-2" />
              导出日志
            </button>
            <button className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              终端设置
            </button>
          </div>
        </div>

        {/* 客户端选择器 */}
        <div className="mb-4">
          <ClientSelector
            selectedClient={selectedClient}
            onClientSelect={setSelectedClient}
          />
        </div>

        {/* 连接状态 */}
        {selectedClient && (
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-gray-600">已连接到: {selectedClient.name}</span>
            </div>
            <div className="flex items-center">
              <Terminal className="h-4 w-4 text-gray-500 mr-2" />
              <span className="text-gray-600">终端类型: {selectedClient.os.includes('Windows') ? 'CMD' : 'Bash'}</span>
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 text-gray-500 mr-2" />
              <span className="text-gray-600">会话时间: 00:15:32</span>
            </div>
          </div>
        )}
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {selectedClient ? (
          <div className="flex-1 flex flex-col">
            {/* 终端输出区域 */}
            <div 
              ref={terminalRef}
              className="flex-1 bg-black text-green-400 font-mono text-sm p-4 overflow-y-auto"
            >
              <div className="mb-4">
                <div className="text-yellow-400">Microsoft Windows [版本 10.0.22621.2715]</div>
                <div className="text-yellow-400">(c) Microsoft Corporation. 保留所有权利。</div>
                <div className="mt-2"></div>
              </div>

              {commandHistory.map((cmd) => (
                <div key={cmd.id} className="mb-4">
                  {/* 命令行 */}
                  <div className="flex items-center mb-1">
                    <span className="text-cyan-400">C:\\Users\\<USER>\\Users\\Administrator&gt;</span>
                <input
                  ref={inputRef}
                  type="text"
                  value={currentCommand}
                  onChange={(e) => setCurrentCommand(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="ml-2 bg-transparent text-white outline-none flex-1 font-mono"
                  placeholder={isRunning ? "正在执行命令..." : "输入命令..."}
                  disabled={isRunning}
                  autoFocus
                />
              </div>
            </div>

            {/* 命令输入区域 */}
            <div className="bg-gray-800 border-t border-gray-600 p-4">
              <div className="flex items-center space-x-2">
                <div className="flex-1 flex items-center bg-gray-700 rounded-md">
                  <Terminal className="h-5 w-5 text-gray-400 ml-3" />
                  <input
                    type="text"
                    value={currentCommand}
                    onChange={(e) => setCurrentCommand(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="输入CMD命令..."
                    className="flex-1 bg-transparent text-white px-3 py-2 outline-none"
                    disabled={isRunning}
                  />
                </div>
                <button
                  onClick={executeCommand}
                  disabled={!currentCommand.trim() || isRunning}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isRunning ? (
                    <>
                      <Square className="h-4 w-4 mr-2" />
                      停止
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      执行
                    </>
                  )}
                </button>
              </div>

              {/* 快捷命令 */}
              <div className="mt-3 flex flex-wrap gap-2">
                {['dir', 'systeminfo', 'ipconfig', 'ping google.com', 'tasklist', 'netstat -an'].map((cmd) => (
                  <button
                    key={cmd}
                    onClick={() => setCurrentCommand(cmd)}
                    className="px-3 py-1 bg-gray-600 text-gray-300 text-sm rounded hover:bg-gray-500"
                    disabled={isRunning}
                  >
                    {cmd}
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Terminal className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">选择客户端</h3>
              <p className="text-gray-500">请先选择一个客户端来执行CMD命令</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

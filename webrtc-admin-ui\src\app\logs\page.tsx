'use client'

import { useState } from 'react'
import { 
  Search, 
  Filter, 
  Download, 
  RefreshCw, 
  Calendar,
  User,
  Monitor,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react'
import { LogEntry } from '@/types'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'

// 模拟日志数据
const mockLogs: LogEntry[] = [
  {
    id: 'log-001',
    timestamp: '2024-01-15 14:30:15',
    operator: '管理员',
    target: 'PC-办公室-001',
    action: '远程连接',
    result: 'success',
    details: '成功建立远程桌面连接'
  },
  {
    id: 'log-002',
    timestamp: '2024-01-15 14:25:30',
    operator: '操作员A',
    target: 'PC-开发-002',
    action: '文件上传',
    result: 'success',
    details: '上传文件: config.json (2.5KB)'
  },
  {
    id: 'log-003',
    timestamp: '2024-01-15 14:20:45',
    operator: '管理员',
    target: 'PC-会议室-003',
    action: '系统重启',
    result: 'failed',
    details: '重启失败: 客户端无响应'
  },
  {
    id: 'log-004',
    timestamp: '2024-01-15 14:15:20',
    operator: '操作员B',
    target: 'PC-办公室-001',
    action: '查看详情',
    result: 'success',
    details: '查看客户端系统信息'
  },
  {
    id: 'log-005',
    timestamp: '2024-01-15 14:10:10',
    operator: '管理员',
    target: '系统',
    action: '用户登录',
    result: 'success',
    details: '管理员登录系统 (IP: ************)'
  },
  {
    id: 'log-006',
    timestamp: '2024-01-15 14:05:35',
    operator: '系统',
    target: 'PC-开发-002',
    action: '状态变更',
    result: 'success',
    details: '客户端状态从离线变更为在线'
  },
  {
    id: 'log-007',
    timestamp: '2024-01-15 14:00:00',
    operator: '操作员A',
    target: 'PC-测试-004',
    action: '远程连接',
    result: 'pending',
    details: '正在尝试建立连接...'
  }
]

export default function LogsPage() {
  const [logs] = useState<LogEntry[]>(mockLogs)
  const [searchQuery, setSearchQuery] = useState('')
  const [operatorFilter, setOperatorFilter] = useState('all')
  const [actionFilter, setActionFilter] = useState('all')
  const [resultFilter, setResultFilter] = useState('all')
  const [dateRange, setDateRange] = useState('today')
  const [selectedLogs, setSelectedLogs] = useState<string[]>([])

  // 筛选日志
  const filteredLogs = logs.filter(log => {
    const matchesSearch = 
      log.operator.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.target.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.details?.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesOperator = operatorFilter === 'all' || log.operator === operatorFilter
    const matchesAction = actionFilter === 'all' || log.action === actionFilter
    const matchesResult = resultFilter === 'all' || log.result === resultFilter
    
    return matchesSearch && matchesOperator && matchesAction && matchesResult
  })

  const handleSelectLog = (logId: string) => {
    setSelectedLogs(prev =>
      prev.includes(logId)
        ? prev.filter(id => id !== logId)
        : [...prev, logId]
    )
  }

  const handleSelectAll = (selected: boolean) => {
    setSelectedLogs(selected ? filteredLogs.map(log => log.id) : [])
  }

  const getResultIcon = (result: LogEntry['result']) => {
    switch (result) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />
    }
  }

  const getResultBadge = (result: LogEntry['result']) => {
    switch (result) {
      case 'success':
        return <Badge variant="success">成功</Badge>
      case 'failed':
        return <Badge variant="danger">失败</Badge>
      case 'pending':
        return <Badge variant="warning">进行中</Badge>
      default:
        return <Badge variant="default">未知</Badge>
    }
  }

  const getActionIcon = (action: string) => {
    if (action.includes('连接')) return <Monitor className="h-4 w-4" />
    if (action.includes('登录')) return <User className="h-4 w-4" />
    return <AlertCircle className="h-4 w-4" />
  }

  const operators = Array.from(new Set(logs.map(log => log.operator)))
  const actions = Array.from(new Set(logs.map(log => log.action)))

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">操作日志</h1>
            <p className="text-sm text-gray-500 mt-1">
              共 {filteredLogs.length} 条记录
              {selectedLogs.length > 0 && ` • 已选择 ${selectedLogs.length} 条`}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              导出
            </Button>
          </div>
        </div>
      </div>

      {/* 筛选栏 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* 搜索框 */}
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索操作员、目标、操作..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 placeholder-gray-500 bg-white"
              />
            </div>
          </div>

          {/* 操作员筛选 */}
          <div>
            <select
              value={operatorFilter}
              onChange={(e) => setOperatorFilter(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white"
            >
              <option value="all">全部操作员</option>
              {operators.map(operator => (
                <option key={operator} value={operator}>{operator}</option>
              ))}
            </select>
          </div>

          {/* 操作类型筛选 */}
          <div>
            <select
              value={actionFilter}
              onChange={(e) => setActionFilter(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white"
            >
              <option value="all">全部操作</option>
              {actions.map(action => (
                <option key={action} value={action}>{action}</option>
              ))}
            </select>
          </div>

          {/* 结果筛选 */}
          <div>
            <select
              value={resultFilter}
              onChange={(e) => setResultFilter(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white"
            >
              <option value="all">全部结果</option>
              <option value="success">成功</option>
              <option value="failed">失败</option>
              <option value="pending">进行中</option>
            </select>
          </div>
        </div>

        {/* 时间范围筛选 */}
        <div className="mt-4 flex items-center space-x-4">
          <Calendar className="h-4 w-4 text-gray-400" />
          <div className="flex items-center space-x-2">
            {['today', '7days', '30days', 'custom'].map(range => (
              <button
                key={range}
                onClick={() => setDateRange(range)}
                className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                  dateRange === range
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {range === 'today' && '今天'}
                {range === '7days' && '最近7天'}
                {range === '30days' && '最近30天'}
                {range === 'custom' && '自定义'}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 日志表格 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={filteredLogs.length > 0 && selectedLogs.length === filteredLogs.length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作员
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  目标
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  结果
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  详情
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredLogs.map((log) => (
                <tr key={log.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedLogs.includes(log.id)}
                      onChange={() => handleSelectLog(log.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {log.timestamp}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <User className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm font-medium text-gray-900">{log.operator}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {log.target}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getActionIcon(log.action)}
                      <span className="ml-2 text-sm text-gray-900">{log.action}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getResultIcon(log.result)}
                      <span className="ml-2">{getResultBadge(log.result)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                    {log.details}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页 */}
        <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              显示 <span className="font-medium">1</span> 到 <span className="font-medium">{filteredLogs.length}</span> 条，
              共 <span className="font-medium">{filteredLogs.length}</span> 条记录
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" disabled>
                上一页
              </Button>
              <Button variant="outline" size="sm" disabled>
                下一页
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

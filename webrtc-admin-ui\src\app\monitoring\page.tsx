'use client'

import { useState, useEffect } from 'react'
import { 
  Cpu, 
  MemoryStick, 
  HardDrive, 
  Wifi, 
  Monitor,
  Clock,
  Thermometer,
  Activity
} from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts'
import MetricCard from '@/components/monitoring/MetricCard'
import ProgressBar from '@/components/monitoring/ProgressBar'
import { formatBytes } from '@/lib/utils'

// 模拟实时数据
const generateTimeSeriesData = (points: number = 20) => {
  const data = []
  const now = new Date()
  
  for (let i = points - 1; i >= 0; i--) {
    const time = new Date(now.getTime() - i * 30000) // 每30秒一个点
    data.push({
      time: time.toLocaleTimeString(),
      cpu: Math.random() * 100,
      memory: Math.random() * 100,
      network: Math.random() * 1000,
      disk: Math.random() * 100
    })
  }
  
  return data
}

export default function MonitoringPage() {
  const [selectedClient, setSelectedClient] = useState('client-001')
  const [timeSeriesData, setTimeSeriesData] = useState(generateTimeSeriesData())
  const [currentMetrics, setCurrentMetrics] = useState({
    cpu: { usage: 45, cores: 8, model: 'Intel Core i7-12700K', temperature: 65 },
    memory: { total: 32 * 1024 * 1024 * 1024, used: 22 * 1024 * 1024 * 1024, available: 10 * 1024 * 1024 * 1024 },
    disk: { total: 1024 * 1024 * 1024 * 1024, used: 768 * 1024 * 1024 * 1024, available: 256 * 1024 * 1024 * 1024 },
    network: { upload: 1024, download: 2048 },
    uptime: 86400 * 3 + 3600 * 5 + 60 * 30
  })

  const clients = [
    { id: 'client-001', name: 'PC-办公室-001', status: 'online' },
    { id: 'client-002', name: 'PC-开发-002', status: 'online' },
    { id: 'client-003', name: 'PC-会议室-003', status: 'offline' }
  ]

  // 模拟实时数据更新
  useEffect(() => {
    const interval = setInterval(() => {
      setTimeSeriesData(prev => {
        const newData = [...prev.slice(1)]
        const now = new Date()
        newData.push({
          time: now.toLocaleTimeString(),
          cpu: Math.random() * 100,
          memory: Math.random() * 100,
          network: Math.random() * 1000,
          disk: Math.random() * 100
        })
        return newData
      })
      
      // 更新当前指标
      setCurrentMetrics(prev => ({
        ...prev,
        cpu: { ...prev.cpu, usage: Math.random() * 100, temperature: 60 + Math.random() * 20 },
        network: { upload: Math.random() * 2048, download: Math.random() * 4096 }
      }))
    }, 30000) // 每30秒更新一次

    return () => clearInterval(interval)
  }, [])

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${days}天 ${hours}小时 ${minutes}分钟`
  }

  return (
    <div className="space-y-6">
      {/* 顶部选择器 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">系统资源监控</h1>
            <p className="text-sm text-gray-500 mt-1">实时监控客户端系统资源使用情况</p>
          </div>
          <div className="flex items-center space-x-4">
            <select
              value={selectedClient}
              onChange={(e) => setSelectedClient(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white"
            >
              {clients.map(client => (
                <option key={client.id} value={client.id}>
                  {client.name} ({client.status === 'online' ? '在线' : '离线'})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="CPU 使用率"
          value={currentMetrics.cpu.usage.toFixed(1)}
          unit="%"
          change={2.5}
          changeLabel="vs 上小时"
          icon={<Cpu className="h-6 w-6" />}
          color={currentMetrics.cpu.usage > 80 ? 'red' : currentMetrics.cpu.usage > 60 ? 'yellow' : 'green'}
        >
          <ProgressBar value={currentMetrics.cpu.usage} />
          <div className="mt-2 text-xs text-gray-500">
            {currentMetrics.cpu.cores} 核心 • {currentMetrics.cpu.temperature}°C
          </div>
        </MetricCard>

        <MetricCard
          title="内存使用"
          value={((currentMetrics.memory.used / currentMetrics.memory.total) * 100).toFixed(1)}
          unit="%"
          change={-1.2}
          changeLabel="vs 上小时"
          icon={<MemoryStick className="h-6 w-6" />}
          color="blue"
        >
          <ProgressBar value={(currentMetrics.memory.used / currentMetrics.memory.total) * 100} />
          <div className="mt-2 text-xs text-gray-500">
            {formatBytes(currentMetrics.memory.used)} / {formatBytes(currentMetrics.memory.total)}
          </div>
        </MetricCard>

        <MetricCard
          title="磁盘使用"
          value={((currentMetrics.disk.used / currentMetrics.disk.total) * 100).toFixed(1)}
          unit="%"
          change={0.8}
          changeLabel="vs 昨天"
          icon={<HardDrive className="h-6 w-6" />}
          color="yellow"
        >
          <ProgressBar value={(currentMetrics.disk.used / currentMetrics.disk.total) * 100} />
          <div className="mt-2 text-xs text-gray-500">
            {formatBytes(currentMetrics.disk.available)} 可用
          </div>
        </MetricCard>

        <MetricCard
          title="网络流量"
          value={formatBytes(currentMetrics.network.download + currentMetrics.network.upload)}
          unit="/s"
          change={15.3}
          changeLabel="vs 上小时"
          icon={<Wifi className="h-6 w-6" />}
          color="green"
        >
          <div className="mt-2 space-y-1">
            <div className="flex justify-between text-xs">
              <span className="text-gray-500">下载</span>
              <span className="font-medium">{formatBytes(currentMetrics.network.download)}/s</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-500">上传</span>
              <span className="font-medium">{formatBytes(currentMetrics.network.upload)}/s</span>
            </div>
          </div>
        </MetricCard>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* CPU 和内存趋势 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">CPU & 内存使用趋势</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="cpu" 
                stroke="#3b82f6" 
                strokeWidth={2}
                name="CPU (%)"
              />
              <Line 
                type="monotone" 
                dataKey="memory" 
                stroke="#10b981" 
                strokeWidth={2}
                name="内存 (%)"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* 网络流量趋势 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">网络流量趋势</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              <Area 
                type="monotone" 
                dataKey="network" 
                stroke="#8b5cf6" 
                fill="#8b5cf6" 
                fillOpacity={0.3}
                name="网络 (KB/s)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 系统信息 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 系统详情 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">系统信息</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">操作系统</span>
              <span className="text-sm font-medium">Windows 11 Pro</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">处理器</span>
              <span className="text-sm font-medium">{currentMetrics.cpu.model}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">运行时间</span>
              <span className="text-sm font-medium">{formatUptime(currentMetrics.uptime)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">最后更新</span>
              <span className="text-sm font-medium">{new Date().toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* 进程信息 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">热门进程</h3>
          <div className="space-y-3">
            {[
              { name: 'chrome.exe', cpu: 15.2, memory: 1024 },
              { name: 'code.exe', cpu: 8.7, memory: 512 },
              { name: 'node.exe', cpu: 5.3, memory: 256 },
              { name: 'explorer.exe', cpu: 2.1, memory: 128 }
            ].map((process, index) => (
              <div key={index} className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium">{process.name}</div>
                  <div className="text-xs text-gray-500">{formatBytes(process.memory * 1024 * 1024)}</div>
                </div>
                <div className="text-sm font-medium">{process.cpu}%</div>
              </div>
            ))}
          </div>
        </div>

        {/* 警告和通知 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">系统警告</h3>
          <div className="space-y-3">
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center">
                <Thermometer className="h-4 w-4 text-yellow-600 mr-2" />
                <span className="text-sm font-medium text-yellow-800">CPU 温度偏高</span>
              </div>
              <p className="text-xs text-yellow-700 mt-1">当前温度 {currentMetrics.cpu.temperature}°C</p>
            </div>
            
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <Activity className="h-4 w-4 text-blue-600 mr-2" />
                <span className="text-sm font-medium text-blue-800">系统运行正常</span>
              </div>
              <p className="text-xs text-blue-700 mt-1">所有服务运行正常</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

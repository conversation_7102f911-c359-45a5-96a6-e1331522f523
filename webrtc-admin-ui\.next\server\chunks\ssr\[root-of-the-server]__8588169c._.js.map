{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, Bell, User, Settings, LogOut, Menu } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\ninterface HeaderProps {\n  onMenuToggle: () => void\n  isSidebarOpen: boolean\n}\n\nexport default function Header({ onMenuToggle, isSidebarOpen }: HeaderProps) {\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)\n  const [searchQuery, setSearchQuery] = useState('')\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 h-16 flex items-center justify-between px-4 lg:px-6 sticky top-0 z-40\">\n      {/* 左侧：Logo 和菜单按钮 */}\n      <div className=\"flex items-center space-x-4\">\n        <button\n          onClick={onMenuToggle}\n          className=\"lg:hidden p-2 rounded-md hover:bg-gray-100 transition-colors\"\n        >\n          <Menu className=\"h-5 w-5\" />\n        </button>\n        \n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n            <span className=\"text-white font-bold text-sm\">W</span>\n          </div>\n          <div className=\"hidden sm:block\">\n            <h1 className=\"text-lg font-semibold text-gray-900\">WebRTC 远程管理</h1>\n            <p className=\"text-xs text-gray-500\">Remote Desktop Management</p>\n          </div>\n        </div>\n      </div>\n\n      {/* 中间：搜索框 */}\n      <div className=\"flex-1 max-w-md mx-4\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"搜索客户端、IP地址...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all text-gray-900 placeholder-gray-500 bg-white\"\n          />\n        </div>\n      </div>\n\n      {/* 右侧：通知和用户菜单 */}\n      <div className=\"flex items-center space-x-3\">\n        {/* 通知按钮 */}\n        <button className=\"relative p-2 rounded-lg hover:bg-gray-100 transition-colors\">\n          <Bell className=\"h-5 w-5 text-gray-600\" />\n          <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n            3\n          </span>\n        </button>\n\n        {/* 用户菜单 */}\n        <div className=\"relative\">\n          <button\n            onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n            className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n          >\n            <div className=\"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\">\n              <User className=\"h-4 w-4 text-gray-600\" />\n            </div>\n            <div className=\"hidden sm:block text-left\">\n              <p className=\"text-sm font-medium text-gray-900\">管理员</p>\n              <p className=\"text-xs text-gray-500\"><EMAIL></p>\n            </div>\n          </button>\n\n          {/* 用户下拉菜单 */}\n          {isUserMenuOpen && (\n            <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50\">\n              <a\n                href=\"#\"\n                className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                <User className=\"h-4 w-4 mr-3\" />\n                个人资料\n              </a>\n              <a\n                href=\"#\"\n                className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                <Settings className=\"h-4 w-4 mr-3\" />\n                设置\n              </a>\n              <hr className=\"my-1\" />\n              <a\n                href=\"#\"\n                className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                <LogOut className=\"h-4 w-4 mr-3\" />\n                退出登录\n              </a>\n            </div>\n          )}\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAWe,SAAS,OAAO,EAAE,YAAY,EAAE,aAAa,EAAe;IACzE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAA+G;;;;;;;;;;;;kCAMjI,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;4BAKxC,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC;wCAAG,WAAU;;;;;;kDACd,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatBytes(bytes: number, decimals = 2) {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const dm = decimals < 0 ? 0 : decimals\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']\n  \n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]\n}\n\nexport function formatUptime(seconds: number) {\n  const days = Math.floor(seconds / 86400)\n  const hours = Math.floor((seconds % 86400) / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  \n  if (days > 0) {\n    return `${days}天 ${hours}小时`\n  } else if (hours > 0) {\n    return `${hours}小时 ${minutes}分钟`\n  } else {\n    return `${minutes}分钟`\n  }\n}\n\nexport function getStatusColor(status: 'online' | 'offline' | 'connecting') {\n  switch (status) {\n    case 'online':\n      return 'text-green-500'\n    case 'offline':\n      return 'text-gray-500'\n    case 'connecting':\n      return 'text-yellow-500'\n    default:\n      return 'text-gray-500'\n  }\n}\n\nexport function getStatusBadgeColor(status: 'online' | 'offline' | 'connecting') {\n  switch (status) {\n    case 'online':\n      return 'bg-green-100 text-green-800 border-green-200'\n    case 'offline':\n      return 'bg-gray-100 text-gray-800 border-gray-200'\n    case 'connecting':\n      return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n    default:\n      return 'bg-gray-100 text-gray-800 border-gray-200'\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAW,CAAC;IACrD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAEO,SAAS,aAAa,OAAe;IAC1C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;IAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,UAAU,QAAS;IAC7C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAE9C,IAAI,OAAO,GAAG;QACZ,OAAO,GAAG,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC;IAC9B,OAAO,IAAI,QAAQ,GAAG;QACpB,OAAO,GAAG,MAAM,GAAG,EAAE,QAAQ,EAAE,CAAC;IAClC,OAAO;QACL,OAAO,GAAG,QAAQ,EAAE,CAAC;IACvB;AACF;AAEO,SAAS,eAAe,MAA2C;IACxE,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAAS,oBAAoB,MAA2C;IAC7E,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport {\n  Monitor,\n  Users,\n  Activity,\n  FileText,\n  Settings,\n  Shield,\n  ChevronDown,\n  ChevronRight,\n  X\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { NavigationItem } from '@/types'\n\ninterface SidebarProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nconst navigationItems: NavigationItem[] = [\n  {\n    id: 'clients',\n    label: '客户端管理',\n    icon: 'Monitor',\n    href: '/',\n    badge: 12\n  },\n  {\n    id: 'remote',\n    label: '远程桌面',\n    icon: 'Monitor',\n    href: '/remote'\n  },\n  {\n    id: 'monitoring',\n    label: '资源监控',\n    icon: 'Activity',\n    href: '/monitoring'\n  },\n  {\n    id: 'logs',\n    label: '日志记录',\n    icon: 'FileText',\n    href: '/logs'\n  },\n  {\n    id: 'users',\n    label: '用户管理',\n    icon: 'Users',\n    href: '/users'\n  },\n  {\n    id: 'settings',\n    label: '系统设置',\n    icon: 'Settings',\n    href: '/settings'\n  }\n]\n\nconst iconMap = {\n  Monitor,\n  Users,\n  Activity,\n  FileText,\n  Settings,\n  Shield\n}\n\nexport default function Sidebar({ isOpen, onClose }: SidebarProps) {\n  const pathname = usePathname()\n  const [expandedItems, setExpandedItems] = useState<string[]>(['settings'])\n\n  const toggleExpanded = (itemId: string) => {\n    setExpandedItems(prev =>\n      prev.includes(itemId)\n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId]\n    )\n  }\n\n  const renderNavigationItem = (item: NavigationItem, level = 0) => {\n    const Icon = iconMap[item.icon as keyof typeof iconMap]\n    const isActive = pathname === item.href\n    const isExpanded = expandedItems.includes(item.id)\n    const hasChildren = item.children && item.children.length > 0\n\n    return (\n      <div key={item.id}>\n        <div\n          className={cn(\n            'flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors',\n            level > 0 && 'ml-4',\n            isActive\n              ? 'bg-blue-100 text-blue-700'\n              : 'text-gray-700 hover:bg-gray-100'\n          )}\n        >\n          <Link\n            href={item.href}\n            className=\"flex items-center flex-1\"\n            onClick={() => {\n              if (window.innerWidth < 1024) {\n                onClose()\n              }\n            }}\n          >\n            <Icon className=\"h-5 w-5 mr-3\" />\n            <span>{item.label}</span>\n            {item.badge && (\n              <span className=\"ml-auto bg-blue-500 text-white text-xs px-2 py-1 rounded-full\">\n                {item.badge}\n              </span>\n            )}\n          </Link>\n          \n          {hasChildren && (\n            <button\n              onClick={() => toggleExpanded(item.id)}\n              className=\"p-1 hover:bg-gray-200 rounded\"\n            >\n              {isExpanded ? (\n                <ChevronDown className=\"h-4 w-4\" />\n              ) : (\n                <ChevronRight className=\"h-4 w-4\" />\n              )}\n            </button>\n          )}\n        </div>\n\n        {hasChildren && isExpanded && (\n          <div className=\"mt-1 space-y-1\">\n            {item.children!.map(child => renderNavigationItem(child, level + 1))}\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  return (\n    <>\n      {/* 移动端遮罩 */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* 侧边栏 */}\n      <aside\n        className={cn(\n          'fixed top-0 left-0 z-50 h-full w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:z-auto lg:h-auto',\n          isOpen ? 'translate-x-0' : '-translate-x-full'\n        )}\n      >\n        {/* 侧边栏头部 */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 lg:hidden\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">导航菜单</h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-md hover:bg-gray-100\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n        </div>\n\n        {/* 导航菜单 */}\n        <nav className=\"flex flex-col h-full\">\n          <div className=\"flex-1 p-4 space-y-1 overflow-y-auto\">\n            {navigationItems.map(item => renderNavigationItem(item))}\n          </div>\n\n          {/* 底部状态信息 */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"bg-gray-50 rounded-lg p-3\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-gray-600\">在线客户端</span>\n                <span className=\"font-medium text-green-600\">12/15</span>\n              </div>\n              <div className=\"flex items-center justify-between text-sm mt-1\">\n                <span className=\"text-gray-600\">系统状态</span>\n                <span className=\"font-medium text-green-600\">正常</span>\n              </div>\n            </div>\n          </div>\n        </nav>\n      </aside>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAhBA;;;;;;;AAwBA,MAAM,kBAAoC;IACxC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;CACD;AAED,MAAM,UAAU;IACd,SAAA,wMAAA,CAAA,UAAO;IACP,OAAA,oMAAA,CAAA,QAAK;IACL,UAAA,0MAAA,CAAA,WAAQ;IACR,UAAA,8MAAA,CAAA,WAAQ;IACR,UAAA,0MAAA,CAAA,WAAQ;IACR,QAAA,sMAAA,CAAA,SAAM;AACR;AAEe,SAAS,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAgB;IAC/D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAW;IAEzE,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,UACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,UACzB;mBAAI;gBAAM;aAAO;IAEzB;IAEA,MAAM,uBAAuB,CAAC,MAAsB,QAAQ,CAAC;QAC3D,MAAM,OAAO,OAAO,CAAC,KAAK,IAAI,CAAyB;QACvD,MAAM,WAAW,aAAa,KAAK,IAAI;QACvC,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;QACjD,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAE5D,qBACE,8OAAC;;8BACC,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,QAAQ,KAAK,QACb,WACI,8BACA;;sCAGN,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS;gCACP,IAAI,OAAO,UAAU,GAAG,MAAM;oCAC5B;gCACF;4BACF;;8CAEA,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,KAAK,KAAK;;;;;;gCAChB,KAAK,KAAK,kBACT,8OAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;;;;;;;wBAKhB,6BACC,8OAAC;4BACC,SAAS,IAAM,eAAe,KAAK,EAAE;4BACrC,WAAU;sCAET,2BACC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAM/B,eAAe,4BACd,8OAAC;oBAAI,WAAU;8BACZ,KAAK,QAAQ,CAAE,GAAG,CAAC,CAAA,QAAS,qBAAqB,OAAO,QAAQ;;;;;;;WA5C7D,KAAK,EAAE;;;;;IAiDrB;IAEA,qBACE;;YAEG,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gLACA,SAAS,kBAAkB;;kCAI7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAA,OAAQ,qBAAqB;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7D", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Header from './Header'\nimport Sidebar from './Sidebar'\n\ninterface LayoutProps {\n  children: React.ReactNode\n}\n\nexport default function Layout({ children }: LayoutProps) {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false)\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen)\n  }\n\n  const closeSidebar = () => {\n    setIsSidebarOpen(false)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 顶部导航栏 */}\n      <Header onMenuToggle={toggleSidebar} isSidebarOpen={isSidebarOpen} />\n\n      <div className=\"flex\">\n        {/* 侧边栏 */}\n        <Sidebar isOpen={isSidebarOpen} onClose={closeSidebar} />\n\n        {/* 主内容区域 */}\n        <main className=\"flex-1 min-h-[calc(100vh-4rem)]\">\n          <div className=\"p-4 lg:p-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,gBAAgB;QACpB,iBAAiB,CAAC;IACpB;IAEA,MAAM,eAAe;QACnB,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,sIAAA,CAAA,UAAM;gBAAC,cAAc;gBAAe,eAAe;;;;;;0BAEpD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,uIAAA,CAAA,UAAO;wBAAC,QAAQ;wBAAe,SAAS;;;;;;kCAGzC,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}
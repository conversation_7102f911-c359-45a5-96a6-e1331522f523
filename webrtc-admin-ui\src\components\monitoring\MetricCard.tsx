import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface MetricCardProps {
  title: string
  value: string | number
  unit?: string
  change?: number
  changeLabel?: string
  icon: ReactNode
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'gray'
  children?: ReactNode
}

export default function MetricCard({
  title,
  value,
  unit,
  change,
  changeLabel,
  icon,
  color = 'blue',
  children
}: MetricCardProps) {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600 border-blue-200',
    green: 'bg-green-50 text-green-600 border-green-200',
    yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
    red: 'bg-red-50 text-red-600 border-red-200',
    gray: 'bg-gray-50 text-gray-600 border-gray-200'
  }

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-red-600'
    if (change < 0) return 'text-green-600'
    return 'text-gray-600'
  }

  return (
    <div className="bg-white rounded-lg shadow border border-gray-200 p-6 hover-lift">
      <div className="flex items-center justify-between mb-4">
        <div className={cn(
          'p-3 rounded-lg border',
          colorClasses[color]
        )}>
          {icon}
        </div>
        {change !== undefined && (
          <div className={cn('text-sm font-medium', getChangeColor(change))}>
            {change > 0 ? '+' : ''}{change}%
            {changeLabel && <span className="text-gray-500 ml-1">{changeLabel}</span>}
          </div>
        )}
      </div>
      
      <div className="mb-2">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        <div className="flex items-baseline space-x-1">
          <span className="text-2xl font-bold text-gray-900">{value}</span>
          {unit && <span className="text-sm text-gray-500">{unit}</span>}
        </div>
      </div>
      
      {children}
    </div>
  )
}

'use client'

import { useState, useRef, useEffect } from 'react'
import {
  Maximize,
  Minimize,
  Volume2,
  VolumeX,
  Settings,
  Upload,
  Download,
  Terminal,
  Clipboard,
  Power,
  RotateCcw,
  Monitor,
  Wifi,
  WifiOff
} from 'lucide-react'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import ClientSelector from '@/components/remote/ClientSelector'
import { useLog } from '@/contexts/LogContext'

interface RemoteSession {
  clientId: string
  clientName: string
  clientIP: string
  status: 'connecting' | 'connected' | 'disconnected'
  quality: 'high' | 'medium' | 'low'
  resolution: string
  startTime: string
}

interface Client {
  id: string
  name: string
  ip: string
  status: 'online' | 'offline'
  os: string
  location: string
}

export default function RemotePage() {
  const { addLog } = useLog()

  const [selectedClient, setSelectedClient] = useState<Client | null>({
    id: 'client-001',
    name: 'PC-办公室-001',
    ip: '*************',
    status: 'online',
    os: 'Windows 11 Pro',
    location: '北京市 海淀区'
  })

  const [session, setSession] = useState<RemoteSession>({
    clientId: 'client-001',
    clientName: 'PC-办公室-001',
    clientIP: '*************',
    status: 'connected',
    quality: 'high',
    resolution: '1920x1080',
    startTime: '2024-01-15 14:30:00'
  })

  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isControlEnabled, setIsControlEnabled] = useState(true)
  const [showSettings, setShowSettings] = useState(false)
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleFullscreen = () => {
    if (!isFullscreen) {
      containerRef.current?.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
    setIsFullscreen(!isFullscreen)
  }

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client)
    if (client.status === 'online') {
      setSession(prev => ({
        ...prev,
        clientId: client.id,
        clientName: client.name,
        clientIP: client.ip,
        status: 'connecting'
      }))
      addLog(`正在连接到 ${client.name}...`, 'info', 'remote')

      // 模拟连接过程
      setTimeout(() => {
        setSession(prev => ({ ...prev, status: 'connected' }))
        addLog(`已连接到 ${client.name}`, 'success', 'remote')
      }, 1500)
    } else {
      addLog(`客户端 ${client.name} 离线，无法连接`, 'warning', 'remote')
    }
  }

  const handleDisconnect = () => {
    setSession(prev => ({ ...prev, status: 'disconnected' }))
    addLog(`与 ${session.clientName} 的连接已断开`, 'warning', 'remote')
  }

  const handleReconnect = () => {
    setSession(prev => ({ ...prev, status: 'connecting' }))
    addLog(`正在重新连接到 ${session.clientName}...`, 'info', 'remote')

    setTimeout(() => {
      setSession(prev => ({ ...prev, status: 'connected' }))
      addLog(`重新连接到 ${session.clientName} 成功`, 'success', 'remote')
    }, 2000)
  }

  const getStatusBadge = () => {
    switch (session.status) {
      case 'connected':
        return <Badge variant="success">已连接</Badge>
      case 'connecting':
        return <Badge variant="warning">连接中</Badge>
      case 'disconnected':
        return <Badge variant="danger">已断开</Badge>
    }
  }

  return (
    <div className="h-[calc(100vh-8rem)] flex flex-col space-y-4">
      {/* 顶部控制栏 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          {/* 左侧：客户端选择器 */}
          <div className="flex items-center space-x-4">
            <ClientSelector
              selectedClient={selectedClient}
              onClientSelect={handleClientSelect}
            />
            {getStatusBadge()}
          </div>

          {/* 中间：连接信息 */}
          {session.status === 'connected' && (
            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <span>分辨率: {session.resolution}</span>
              <span>质量: {session.quality.toUpperCase()}</span>
              <span>延迟: 45ms</span>
              <span>帧率: 60fps</span>
            </div>
          )}

          {/* 右侧：控制按钮 */}
          <div className="flex items-center space-x-2">
            {/* 快捷操作按钮 */}
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-1" />
              上传
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-1" />
              下载
            </Button>
            <Button variant="outline" size="sm">
              <Terminal className="h-4 w-4 mr-1" />
              终端
            </Button>
            <Button variant="outline" size="sm">
              <Clipboard className="h-4 w-4 mr-1" />
              剪贴板
            </Button>

            {/* 分隔线 */}
            <div className="h-6 w-px bg-gray-300 mx-2"></div>

            {/* 控制按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsControlEnabled(!isControlEnabled)}
            >
              {isControlEnabled ? (
                <>
                  <Wifi className="h-4 w-4 mr-1" />
                  控制
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 mr-1" />
                  控制
                </>
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsMuted(!isMuted)}
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleFullscreen}
            >
              {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>

            {session.status === 'connected' ? (
              <Button variant="danger" size="sm" onClick={handleDisconnect}>
                <Power className="h-4 w-4 mr-1" />
                断开
              </Button>
            ) : (
              <Button variant="primary" size="sm" onClick={handleReconnect}>
                <RotateCcw className="h-4 w-4 mr-1" />
                连接
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 主视频区域 - 铺满整个剩余空间 */}
      <div className="flex-1 bg-black rounded-lg overflow-hidden relative" ref={containerRef}>
        {session.status === 'connected' ? (
          <div className="w-full h-full flex items-center justify-center bg-gray-900">
            <div className="text-center text-white">
              <Monitor className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg">远程桌面画面</p>
              <p className="text-sm opacity-75 mt-2">
                WebRTC 视频流将在这里显示
              </p>
            </div>
          </div>
        ) : session.status === 'connecting' ? (
          <div className="w-full h-full flex items-center justify-center bg-gray-800">
            <div className="text-center text-white">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-lg">正在连接...</p>
              <p className="text-sm opacity-75 mt-2">
                正在连接到 {session.clientName}
              </p>
            </div>
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-700">
            <div className="text-center text-white">
              <WifiOff className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg">未连接</p>
              <p className="text-sm opacity-75 mt-2 mb-4">
                请选择一个在线客户端进行连接
              </p>
              {selectedClient?.status === 'online' && (
                <Button variant="primary" onClick={handleReconnect}>
                  连接到 {selectedClient.name}
                </Button>
              )}
            </div>
          </div>
        )}

        {/* 设置面板覆盖层 */}
        {showSettings && session.status === 'connected' && (
          <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-4 w-64">
            <h3 className="text-sm font-medium text-gray-900 mb-3">显示设置</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  画质
                </label>
                <select
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  value={session.quality}
                  onChange={(e) => {
                    setSession(prev => ({
                      ...prev,
                      quality: e.target.value as 'high' | 'medium' | 'low'
                    }))
                    addLog(`画质已调整为 ${e.target.value}`, 'info', 'remote')
                  }}
                >
                  <option value="high">高质量</option>
                  <option value="medium">中等质量</option>
                  <option value="low">低质量</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">
                  分辨率
                </label>
                <select
                  className="w-full text-sm border border-gray-300 rounded px-2 py-1 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  value={session.resolution}
                  onChange={(e) => {
                    setSession(prev => ({
                      ...prev,
                      resolution: e.target.value
                    }))
                    addLog(`分辨率已调整为 ${e.target.value}`, 'info', 'remote')
                  }}
                >
                  <option value="1920x1080">1920x1080</option>
                  <option value="1366x768">1366x768</option>
                  <option value="1024x768">1024x768</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

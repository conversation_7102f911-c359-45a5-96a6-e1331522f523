'use client'

import { useState, useRef, useEffect } from 'react'
import { 
  Maximize, 
  Minimize, 
  Volume2, 
  VolumeX, 
  Settings, 
  Upload, 
  Download, 
  Terminal,
  Clipboard,
  Power,
  RotateCcw,
  Monitor,
  Wifi,
  WifiOff
} from 'lucide-react'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'

interface RemoteSession {
  clientId: string
  clientName: string
  clientIP: string
  status: 'connecting' | 'connected' | 'disconnected'
  quality: 'high' | 'medium' | 'low'
  resolution: string
  startTime: string
}

export default function RemotePage() {
  const [session, setSession] = useState<RemoteSession>({
    clientId: 'client-001',
    clientName: 'PC-办公室-001',
    clientIP: '*************',
    status: 'connected',
    quality: 'high',
    resolution: '1920x1080',
    startTime: '2024-01-15 14:30:00'
  })
  
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isControlEnabled, setIsControlEnabled] = useState(true)
  const [showSettings, setShowSettings] = useState(false)
  const [logs, setLogs] = useState([
    { time: '14:30:15', message: '正在连接到客户端...', type: 'info' },
    { time: '14:30:18', message: '连接建立成功', type: 'success' },
    { time: '14:30:20', message: '开始接收视频流', type: 'success' },
    { time: '14:30:22', message: '远程控制已启用', type: 'info' }
  ])
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleFullscreen = () => {
    if (!isFullscreen) {
      containerRef.current?.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
    setIsFullscreen(!isFullscreen)
  }

  const handleDisconnect = () => {
    setSession(prev => ({ ...prev, status: 'disconnected' }))
    setLogs(prev => [...prev, { 
      time: new Date().toLocaleTimeString(), 
      message: '连接已断开', 
      type: 'warning' 
    }])
  }

  const handleReconnect = () => {
    setSession(prev => ({ ...prev, status: 'connecting' }))
    setLogs(prev => [...prev, { 
      time: new Date().toLocaleTimeString(), 
      message: '正在重新连接...', 
      type: 'info' 
    }])
    
    setTimeout(() => {
      setSession(prev => ({ ...prev, status: 'connected' }))
      setLogs(prev => [...prev, { 
        time: new Date().toLocaleTimeString(), 
        message: '重新连接成功', 
        type: 'success' 
      }])
    }, 2000)
  }

  const getStatusBadge = () => {
    switch (session.status) {
      case 'connected':
        return <Badge variant="success">已连接</Badge>
      case 'connecting':
        return <Badge variant="warning">连接中</Badge>
      case 'disconnected':
        return <Badge variant="danger">已断开</Badge>
    }
  }

  return (
    <div className="h-[calc(100vh-8rem)] flex flex-col space-y-4">
      {/* 顶部控制栏 */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Monitor className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                {session.clientName}
              </h1>
              <p className="text-sm text-gray-500">
                {session.clientIP} • {session.resolution} • {session.quality.toUpperCase()}
              </p>
            </div>
            {getStatusBadge()}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsControlEnabled(!isControlEnabled)}
            >
              {isControlEnabled ? (
                <>
                  <Wifi className="h-4 w-4 mr-2" />
                  控制已启用
                </>
              ) : (
                <>
                  <WifiOff className="h-4 w-4 mr-2" />
                  控制已禁用
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsMuted(!isMuted)}
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleFullscreen}
            >
              {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
            
            {session.status === 'connected' ? (
              <Button variant="danger" size="sm" onClick={handleDisconnect}>
                <Power className="h-4 w-4 mr-2" />
                断开连接
              </Button>
            ) : (
              <Button variant="primary" size="sm" onClick={handleReconnect}>
                <RotateCcw className="h-4 w-4 mr-2" />
                重新连接
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 flex space-x-4">
        {/* 主视频区域 */}
        <div className="flex-1 bg-black rounded-lg overflow-hidden relative" ref={containerRef}>
          {session.status === 'connected' ? (
            <div className="w-full h-full flex items-center justify-center bg-gray-900">
              <div className="text-center text-white">
                <Monitor className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg">远程桌面画面</p>
                <p className="text-sm opacity-75 mt-2">
                  WebRTC 视频流将在这里显示
                </p>
              </div>
            </div>
          ) : session.status === 'connecting' ? (
            <div className="w-full h-full flex items-center justify-center bg-gray-800">
              <div className="text-center text-white">
                <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
                <p className="text-lg">正在连接...</p>
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-700">
              <div className="text-center text-white">
                <WifiOff className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg">连接已断开</p>
                <Button variant="primary" className="mt-4" onClick={handleReconnect}>
                  重新连接
                </Button>
              </div>
            </div>
          )}
          
          {/* 视频控制覆盖层 */}
          {session.status === 'connected' && (
            <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 rounded-lg p-3 text-white text-sm">
              <div className="flex items-center justify-between">
                <span>分辨率: {session.resolution}</span>
                <span>质量: {session.quality.toUpperCase()}</span>
                <span>延迟: 45ms</span>
                <span>帧率: 60fps</span>
              </div>
            </div>
          )}
        </div>

        {/* 右侧工具栏 */}
        <div className="w-80 space-y-4">
          {/* 快捷操作 */}
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">快捷操作</h3>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                上传文件
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                下载文件
              </Button>
              <Button variant="outline" size="sm">
                <Terminal className="h-4 w-4 mr-2" />
                命令行
              </Button>
              <Button variant="outline" size="sm">
                <Clipboard className="h-4 w-4 mr-2" />
                剪贴板
              </Button>
            </div>
          </div>

          {/* 设置面板 */}
          {showSettings && (
            <div className="bg-white rounded-lg shadow p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-3">显示设置</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    画质
                  </label>
                  <select 
                    className="w-full text-sm border border-gray-300 rounded px-2 py-1 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                    value={session.quality}
                    onChange={(e) => setSession(prev => ({ 
                      ...prev, 
                      quality: e.target.value as 'high' | 'medium' | 'low' 
                    }))}
                  >
                    <option value="high">高质量</option>
                    <option value="medium">中等质量</option>
                    <option value="low">低质量</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    分辨率
                  </label>
                  <select 
                    className="w-full text-sm border border-gray-300 rounded px-2 py-1 text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                    value={session.resolution}
                    onChange={(e) => setSession(prev => ({ 
                      ...prev, 
                      resolution: e.target.value 
                    }))}
                  >
                    <option value="1920x1080">1920x1080</option>
                    <option value="1366x768">1366x768</option>
                    <option value="1024x768">1024x768</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* 操作日志 */}
          <div className="bg-white rounded-lg shadow p-4">
            <h3 className="text-sm font-medium text-gray-900 mb-3">操作日志</h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="text-xs">
                  <span className="text-gray-500">{log.time}</span>
                  <span className={`ml-2 ${
                    log.type === 'success' ? 'text-green-600' :
                    log.type === 'warning' ? 'text-yellow-600' :
                    log.type === 'error' ? 'text-red-600' :
                    'text-gray-600'
                  }`}>
                    {log.message}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

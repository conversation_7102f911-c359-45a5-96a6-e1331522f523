export interface Client {
  id: string
  name: string
  ip: string
  status: 'online' | 'offline'
  os: string
  location: string
  uuid: string
  owner: {
    name: string
    uuid: string
  }
  group: string
  lastSeen: string
  version: string
  cpu: {
    usage: number
    model: string
  }
  memory: {
    usage: number
    total: number
    available: number
  }
  disk: {
    usage: number
    total: number
    available: number
  }
  network: {
    upload: number
    download: number
  }
}

export interface RemoteSession {
  clientId: string
  clientName: string
  clientIP: string
  status: 'connecting' | 'connected' | 'disconnected'
  quality: 'high' | 'medium' | 'low'
  resolution: string
  startTime: string
  duration?: number
}

export interface FileItem {
  id: string
  name: string
  type: 'file' | 'folder'
  size?: number
  modified: string
  path: string
  permissions?: string
}

export interface CommandHistory {
  id: string
  command: string
  output: string
  status: 'success' | 'error' | 'running'
  timestamp: string
  duration?: number
}

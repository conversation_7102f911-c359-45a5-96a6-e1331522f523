{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'\n    }\n    \n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || loading}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport default Button\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/clients/ClientFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, Filter, Download, RefreshCw, Plus } from 'lucide-react'\nimport Button from '@/components/ui/Button'\n\ninterface ClientFiltersProps {\n  searchQuery: string\n  onSearchChange: (query: string) => void\n  statusFilter: 'all' | 'online' | 'offline' | 'connecting'\n  onStatusFilterChange: (status: 'all' | 'online' | 'offline' | 'connecting') => void\n  groupFilter: string\n  onGroupFilterChange: (group: string) => void\n  selectedCount: number\n  onRefresh: () => void\n  onExport: () => void\n  onBatchConnect: () => void\n  onAddClient: () => void\n}\n\nconst statusOptions = [\n  { value: 'all', label: '全部状态', count: 15 },\n  { value: 'online', label: '在线', count: 12 },\n  { value: 'offline', label: '离线', count: 2 },\n  { value: 'connecting', label: '连接中', count: 1 }\n]\n\nconst groupOptions = [\n  { value: 'all', label: '全部分组' },\n  { value: 'production', label: '生产环境' },\n  { value: 'development', label: '开发环境' },\n  { value: 'testing', label: '测试环境' }\n]\n\nexport default function ClientFilters({\n  searchQuery,\n  onSearchChange,\n  statusFilter,\n  onStatusFilterChange,\n  groupFilter,\n  onGroupFilterChange,\n  selectedCount,\n  onRefresh,\n  onExport,\n  onBatchConnect,\n  onAddClient\n}: ClientFiltersProps) {\n  const [isFilterOpen, setIsFilterOpen] = useState(false)\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-4 mb-6 hover-lift\">\n      {/* 顶部操作栏 */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-4\">\n        <div className=\"flex items-center space-x-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">客户端管理</h2>\n          <span className=\"text-sm text-gray-500\">\n            共 {statusOptions.find(s => s.value === 'all')?.count || 0} 台设备\n          </span>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <Button variant=\"outline\" size=\"sm\" onClick={onRefresh}>\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\n            刷新\n          </Button>\n          <Button variant=\"outline\" size=\"sm\" onClick={onExport}>\n            <Download className=\"h-4 w-4 mr-2\" />\n            导出\n          </Button>\n          <Button size=\"sm\" onClick={onAddClient}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            添加客户端\n          </Button>\n        </div>\n      </div>\n\n      {/* 搜索和筛选 */}\n      <div className=\"flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4\">\n        {/* 搜索框 */}\n        <div className=\"flex-1 relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"搜索客户端名称、IP地址...\"\n            value={searchQuery}\n            onChange={(e) => onSearchChange(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 placeholder-gray-500\"\n          />\n        </div>\n\n        {/* 状态筛选 */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-700 whitespace-nowrap\">状态:</span>\n          <select\n            value={statusFilter}\n            onChange={(e) => onStatusFilterChange(e.target.value as any)}\n            className=\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white\"\n          >\n            {statusOptions.map(option => (\n              <option key={option.value} value={option.value}>\n                {option.label} ({option.count})\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* 分组筛选 */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-700 whitespace-nowrap\">分组:</span>\n          <select\n            value={groupFilter}\n            onChange={(e) => onGroupFilterChange(e.target.value)}\n            className=\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white\"\n          >\n            {groupOptions.map(option => (\n              <option key={option.value} value={option.value}>\n                {option.label}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* 高级筛选按钮 */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setIsFilterOpen(!isFilterOpen)}\n        >\n          <Filter className=\"h-4 w-4 mr-2\" />\n          高级筛选\n        </Button>\n      </div>\n\n      {/* 批量操作栏 */}\n      {selectedCount > 0 && (\n        <div className=\"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-blue-700\">\n              已选择 {selectedCount} 台设备\n            </span>\n            <div className=\"flex items-center space-x-2\">\n              <Button size=\"sm\" variant=\"outline\" onClick={onBatchConnect}>\n                批量连接\n              </Button>\n              <Button size=\"sm\" variant=\"outline\">\n                批量重启\n              </Button>\n              <Button size=\"sm\" variant=\"outline\">\n                添加标签\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 高级筛选面板 */}\n      {isFilterOpen && (\n        <div className=\"mt-4 p-4 bg-gray-50 rounded-lg border\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                操作系统\n              </label>\n              <select className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\">\n                <option value=\"\">全部</option>\n                <option value=\"windows\">Windows</option>\n                <option value=\"linux\">Linux</option>\n                <option value=\"macos\">macOS</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                最后在线时间\n              </label>\n              <select className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\">\n                <option value=\"\">全部</option>\n                <option value=\"1h\">1小时内</option>\n                <option value=\"24h\">24小时内</option>\n                <option value=\"7d\">7天内</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                标签\n              </label>\n              <input\n                type=\"text\"\n                placeholder=\"输入标签名称\"\n                className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 placeholder-gray-500 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n              />\n            </div>\n          </div>\n          <div className=\"mt-4 flex justify-end space-x-2\">\n            <Button variant=\"outline\" size=\"sm\" onClick={() => setIsFilterOpen(false)}>\n              取消\n            </Button>\n            <Button size=\"sm\">\n              应用筛选\n            </Button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAoBA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAO,OAAO;QAAQ,OAAO;IAAG;IACzC;QAAE,OAAO;QAAU,OAAO;QAAM,OAAO;IAAG;IAC1C;QAAE,OAAO;QAAW,OAAO;QAAM,OAAO;IAAE;IAC1C;QAAE,OAAO;QAAc,OAAO;QAAO,OAAO;IAAE;CAC/C;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAO,OAAO;IAAO;IAC9B;QAAE,OAAO;QAAc,OAAO;IAAO;IACrC;QAAE,OAAO;QAAe,OAAO;IAAO;IACtC;QAAE,OAAO;QAAW,OAAO;IAAO;CACnC;AAEc,SAAS,cAAc,EACpC,WAAW,EACX,cAAc,EACd,YAAY,EACZ,oBAAoB,EACpB,WAAW,EACX,mBAAmB,EACnB,aAAa,EACb,SAAS,EACT,QAAQ,EACR,cAAc,EACd,WAAW,EACQ;;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAK,WAAU;;oCAAwB;oCACnC,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,SAAS;oCAAE;;;;;;;;;;;;;kCAI9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,UAAM;gCAAC,MAAK;gCAAK,SAAS;;kDACzB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA0C;;;;;;0CAC1D,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCACpD,WAAU;0CAET,cAAc,GAAG,CAAC,CAAA,uBACjB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;;4CAC3C,OAAO,KAAK;4CAAC;4CAAG,OAAO,KAAK;4CAAC;;uCADnB,OAAO,KAAK;;;;;;;;;;;;;;;;kCAQ/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA0C;;;;;;0CAC1D,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;0CAET,aAAa,GAAG,CAAC,CAAA,uBAChB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDAC3C,OAAO,KAAK;uCADF,OAAO,KAAK;;;;;;;;;;;;;;;;kCAQ/B,6LAAC,qIAAA,CAAA,UAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,gBAAgB,CAAC;;0CAEhC,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMtC,gBAAgB,mBACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;gCAAwB;gCACjC;gCAAc;;;;;;;sCAErB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,SAAS;8CAAgB;;;;;;8CAG7D,6LAAC,qIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAK,SAAQ;8CAAU;;;;;;8CAGpC,6LAAC,qIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAK,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;YAS3C,8BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,6LAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAG1B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,6LAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAK;;;;;;;;;;;;;;;;;;0CAGvB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;kCAIhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS,IAAM,gBAAgB;0CAAQ;;;;;;0CAG3E,6LAAC,qIAAA,CAAA,UAAM;gCAAC,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAQ9B;GA1KwB;KAAA", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/clients/StatusIndicator.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface StatusIndicatorProps {\n  status: 'online' | 'offline' | 'connecting'\n  showText?: boolean\n  size?: 'sm' | 'md' | 'lg'\n}\n\nexport default function StatusIndicator({ \n  status, \n  showText = true, \n  size = 'md' \n}: StatusIndicatorProps) {\n  const statusConfig = {\n    online: {\n      color: 'bg-green-500',\n      text: '在线',\n      textColor: 'text-green-700'\n    },\n    offline: {\n      color: 'bg-gray-400',\n      text: '离线',\n      textColor: 'text-gray-700'\n    },\n    connecting: {\n      color: 'bg-yellow-500',\n      text: '连接中',\n      textColor: 'text-yellow-700'\n    }\n  }\n\n  const sizeConfig = {\n    sm: {\n      dot: 'w-2 h-2',\n      text: 'text-xs'\n    },\n    md: {\n      dot: 'w-3 h-3',\n      text: 'text-sm'\n    },\n    lg: {\n      dot: 'w-4 h-4',\n      text: 'text-base'\n    }\n  }\n\n  const config = statusConfig[status]\n  const sizeClass = sizeConfig[size]\n\n  return (\n    <div className=\"flex items-center space-x-2\">\n      <div\n        className={cn(\n          'rounded-full',\n          config.color,\n          sizeClass.dot,\n          status === 'connecting' && 'animate-pulse'\n        )}\n      />\n      {showText && (\n        <span className={cn(config.textColor, sizeClass.text, 'font-medium')}>\n          {config.text}\n        </span>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAQe,SAAS,gBAAgB,EACtC,MAAM,EACN,WAAW,IAAI,EACf,OAAO,IAAI,EACU;IACrB,MAAM,eAAe;QACnB,QAAQ;YACN,OAAO;YACP,MAAM;YACN,WAAW;QACb;QACA,SAAS;YACP,OAAO;YACP,MAAM;YACN,WAAW;QACb;QACA,YAAY;YACV,OAAO;YACP,MAAM;YACN,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,KAAK;YACL,MAAM;QACR;QACA,IAAI;YACF,KAAK;YACL,MAAM;QACR;QACA,IAAI;YACF,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,SAAS,YAAY,CAAC,OAAO;IACnC,MAAM,YAAY,UAAU,CAAC,KAAK;IAElC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,OAAO,KAAK,EACZ,UAAU,GAAG,EACb,WAAW,gBAAgB;;;;;;YAG9B,0BACC,6LAAC;gBAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO,SAAS,EAAE,UAAU,IAAI,EAAE;0BACnD,OAAO,IAAI;;;;;;;;;;;;AAKtB;KA1DwB", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface BadgeProps extends HTMLAttributes<HTMLSpanElement> {\n  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info'\n  size?: 'xs' | 'sm' | 'md'\n}\n\nexport default function Badge({ \n  className, \n  variant = 'default', \n  size = 'md', \n  children, \n  ...props \n}: BadgeProps) {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full border'\n  \n  const variants = {\n    default: 'bg-gray-100 text-gray-800 border-gray-200',\n    success: 'bg-green-100 text-green-800 border-green-200',\n    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    danger: 'bg-red-100 text-red-800 border-red-200',\n    info: 'bg-blue-100 text-blue-800 border-blue-200'\n  }\n  \n  const sizes = {\n    xs: 'px-1.5 py-0.5 text-xs',\n    sm: 'px-2 py-0.5 text-xs',\n    md: 'px-2.5 py-1 text-sm'\n  }\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,MAAM,EAC5B,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACQ;IACX,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KApCwB", "debugId": null}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/clients/ClientTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { Monitor, MoreVertical, Play, RotateCcw, Eye, Tag, FolderOpen, Terminal, MapPin, User } from 'lucide-react'\nimport { Client } from '@/types'\nimport { formatUptime } from '@/lib/utils'\nimport StatusIndicator from './StatusIndicator'\nimport Button from '@/components/ui/Button'\nimport Badge from '@/components/ui/Badge'\n\ninterface ClientTableProps {\n  clients: Client[]\n  selectedClients: string[]\n  onSelectClient: (clientId: string) => void\n  onSelectAll: (selected: boolean) => void\n  onRemoteConnect: (clientId: string) => void\n  onFileManage: (clientId: string) => void\n  onCmdExecute: (clientId: string) => void\n}\n\nexport default function ClientTable({\n  clients,\n  selectedClients,\n  onSelectClient,\n  onSelectAll,\n  onRemoteConnect,\n  onFileManage,\n  onCmdExecute\n}: ClientTableProps) {\n  const [sortField, setSortField] = useState<keyof Client>('name')\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')\n  const [openDropdown, setOpenDropdown] = useState<string | null>(null)\n\n  // 点击外部关闭下拉菜单\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      const target = event.target as HTMLElement\n      if (!target.closest('.dropdown-container')) {\n        setOpenDropdown(null)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  const handleSort = (field: keyof Client) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')\n    } else {\n      setSortField(field)\n      setSortDirection('asc')\n    }\n  }\n\n  const sortedClients = [...clients].sort((a, b) => {\n    const aValue = a[sortField]\n    const bValue = b[sortField]\n    \n    if (typeof aValue === 'string' && typeof bValue === 'string') {\n      return sortDirection === 'asc' \n        ? aValue.localeCompare(bValue)\n        : bValue.localeCompare(aValue)\n    }\n    \n    if (typeof aValue === 'number' && typeof bValue === 'number') {\n      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue\n    }\n    \n    return 0\n  })\n\n  const isAllSelected = clients.length > 0 && selectedClients.length === clients.length\n\n  return (\n    <div className=\"overflow-x-auto\">\n      <table className=\"w-full divide-y divide-gray-200 table-fixed\"\n             style={{ minWidth: '1200px' }}>\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-4 py-1.5 text-left\" style={{ width: '50px' }}>\n                <input\n                  type=\"checkbox\"\n                  checked={isAllSelected}\n                  onChange={(e) => onSelectAll(e.target.checked)}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n              </th>\n              <th\n                className=\"px-3 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                onClick={() => handleSort('status')}\n                style={{ width: '80px' }}\n              >\n                状态\n              </th>\n              <th\n                className=\"px-4 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                onClick={() => handleSort('name')}\n                style={{ width: '200px' }}\n              >\n                客户端名称\n              </th>\n              <th className=\"px-4 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                  style={{ width: '220px' }}>\n                归属用户\n              </th>\n              <th\n                className=\"px-4 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                onClick={() => handleSort('ip')}\n                style={{ width: '180px' }}\n              >\n                IP地址/位置\n              </th>\n              <th className=\"px-4 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                  style={{ width: '150px' }}>\n                系统信息\n              </th>\n              <th className=\"px-4 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                  style={{ width: '160px' }}>\n                最后上线/运行时间\n              </th>\n              <th className=\"px-4 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                  style={{ width: '120px' }}>\n                标签\n              </th>\n              <th className=\"px-4 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                  style={{ width: '80px' }}>\n                操作\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {sortedClients.map((client) => (\n              <tr key={client.id} className=\"hover:bg-gray-50\">\n                <td className=\"px-4 py-1\" style={{ width: '50px' }}>\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedClients.includes(client.id)}\n                    onChange={() => onSelectClient(client.id)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                </td>\n                <td className=\"px-3 py-1 whitespace-nowrap\" style={{ width: '80px' }}>\n                  <StatusIndicator status={client.status} size=\"sm\" />\n                </td>\n                <td className=\"px-4 py-1 whitespace-nowrap\" style={{ width: '200px' }}>\n                  <div className=\"flex items-center\">\n                    <Monitor className=\"h-4 w-4 text-gray-400 mr-2\" />\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900 leading-tight\">{client.name}</div>\n                      <div className=\"text-xs text-gray-500 leading-tight\">{client.id}</div>\n                    </div>\n                  </div>\n                </td>\n                <td className=\"px-4 py-1 whitespace-nowrap text-sm text-gray-900\" style={{ width: '220px' }}>\n                  <div className=\"flex items-center w-full\">\n                    <User className=\"h-4 w-4 text-gray-400 mr-2 flex-shrink-0\" />\n                    <div className=\"min-w-0 flex-1\">\n                      <div className=\"font-medium text-sm leading-tight\">{client.owner.name}</div>\n                      <div className=\"text-gray-500 text-xs font-mono overflow-hidden leading-tight\" title={client.owner.uuid}>\n                        <span className=\"block truncate max-w-full\">\n                          {client.owner.uuid}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </td>\n                <td className=\"px-4 py-1 whitespace-nowrap text-sm text-gray-900\" style={{ width: '180px' }}>\n                  <div>\n                    <div className=\"font-medium text-sm leading-tight\">{client.ip}</div>\n                    <div className=\"flex items-center text-gray-500 text-xs leading-tight\">\n                      <MapPin className=\"h-3 w-3 mr-1\" />\n                      {client.location}\n                    </div>\n                  </div>\n                </td>\n                <td className=\"px-4 py-1 whitespace-nowrap text-sm text-gray-900\" style={{ width: '150px' }}>\n                  <div>\n                    <div className=\"text-sm leading-tight\">{client.os}</div>\n                    <div className=\"text-gray-500 text-xs leading-tight\">v{client.version}</div>\n                  </div>\n                </td>\n                <td className=\"px-4 py-1 whitespace-nowrap text-sm text-gray-900\" style={{ width: '160px' }}>\n                  <div>\n                    <div className=\"font-medium text-sm leading-tight\">{client.lastOnline}</div>\n                    <div className=\"text-gray-500 text-xs leading-tight\">{formatUptime(client.uptime)}</div>\n                  </div>\n                </td>\n                <td className=\"px-4 py-1 whitespace-nowrap\" style={{ width: '120px' }}>\n                  <div className=\"flex flex-wrap gap-0.5\">\n                    {client.tags.map((tag, index) => (\n                      <Badge key={index} variant=\"info\" size=\"xs\">\n                        {tag}\n                      </Badge>\n                    ))}\n                  </div>\n                </td>\n                <td className=\"px-4 py-1 whitespace-nowrap text-sm font-medium\" style={{ width: '80px' }}>\n                  <div className=\"relative dropdown-container\">\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => setOpenDropdown(openDropdown === client.id ? null : client.id)}\n                    >\n                      <MoreVertical className=\"h-4 w-4\" />\n                    </Button>\n\n                    {openDropdown === client.id && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50\">\n                        <button\n                          onClick={() => {\n                            onRemoteConnect(client.id)\n                            setOpenDropdown(null)\n                          }}\n                          disabled={client.status !== 'online'}\n                          className=\"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          <Monitor className=\"h-4 w-4 mr-3\" />\n                          远程桌面\n                        </button>\n                        <button\n                          onClick={() => {\n                            onFileManage(client.id)\n                            setOpenDropdown(null)\n                          }}\n                          disabled={client.status !== 'online'}\n                          className=\"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          <FolderOpen className=\"h-4 w-4 mr-3\" />\n                          文件管理\n                        </button>\n                        <button\n                          onClick={() => {\n                            onCmdExecute(client.id)\n                            setOpenDropdown(null)\n                          }}\n                          disabled={client.status !== 'online'}\n                          className=\"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          <Terminal className=\"h-4 w-4 mr-3\" />\n                          CMD命令\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n      </table>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;;AARA;;;;;;;AAoBe,SAAS,YAAY,EAClC,OAAO,EACP,eAAe,EACf,cAAc,EACd,WAAW,EACX,eAAe,EACf,YAAY,EACZ,YAAY,EACK;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,MAAM,SAAS,MAAM,MAAM;gBAC3B,IAAI,CAAC,OAAO,OAAO,CAAC,wBAAwB;oBAC1C,gBAAgB;gBAClB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;yCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;gCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA,MAAM,gBAAgB;WAAI;KAAQ,CAAC,IAAI,CAAC,CAAC,GAAG;QAC1C,MAAM,SAAS,CAAC,CAAC,UAAU;QAC3B,MAAM,SAAS,CAAC,CAAC,UAAU;QAE3B,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;YAC5D,OAAO,kBAAkB,QACrB,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;QAC3B;QAEA,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;YAC5D,OAAO,kBAAkB,QAAQ,SAAS,SAAS,SAAS;QAC9D;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB,QAAQ,MAAM,GAAG,KAAK,gBAAgB,MAAM,KAAK,QAAQ,MAAM;IAErF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAM,WAAU;YACV,OAAO;gBAAE,UAAU;YAAS;;8BAC/B,6LAAC;oBAAM,WAAU;8BACf,cAAA,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;gCAAwB,OAAO;oCAAE,OAAO;gCAAO;0CAC3D,cAAA,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,OAAO;oCAC7C,WAAU;;;;;;;;;;;0CAGd,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,WAAW;gCAC1B,OAAO;oCAAE,OAAO;gCAAO;0CACxB;;;;;;0CAGD,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,WAAW;gCAC1B,OAAO;oCAAE,OAAO;gCAAQ;0CACzB;;;;;;0CAGD,6LAAC;gCAAG,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAQ;0CAAG;;;;;;0CAG/B,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,WAAW;gCAC1B,OAAO;oCAAE,OAAO;gCAAQ;0CACzB;;;;;;0CAGD,6LAAC;gCAAG,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAQ;0CAAG;;;;;;0CAG/B,6LAAC;gCAAG,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAQ;0CAAG;;;;;;0CAG/B,6LAAC;gCAAG,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAQ;0CAAG;;;;;;0CAG/B,6LAAC;gCAAG,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAO;0CAAG;;;;;;;;;;;;;;;;;8BAKlC,6LAAC;oBAAM,WAAU;8BACd,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;4BAAmB,WAAU;;8CAC5B,6LAAC;oCAAG,WAAU;oCAAY,OAAO;wCAAE,OAAO;oCAAO;8CAC/C,cAAA,6LAAC;wCACC,MAAK;wCACL,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;wCAC3C,UAAU,IAAM,eAAe,OAAO,EAAE;wCACxC,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAG,WAAU;oCAA8B,OAAO;wCAAE,OAAO;oCAAO;8CACjE,cAAA,6LAAC,mJAAA,CAAA,UAAe;wCAAC,QAAQ,OAAO,MAAM;wCAAE,MAAK;;;;;;;;;;;8CAE/C,6LAAC;oCAAG,WAAU;oCAA8B,OAAO;wCAAE,OAAO;oCAAQ;8CAClE,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAmD,OAAO,IAAI;;;;;;kEAC7E,6LAAC;wDAAI,WAAU;kEAAuC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAIrE,6LAAC;oCAAG,WAAU;oCAAoD,OAAO;wCAAE,OAAO;oCAAQ;8CACxF,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqC,OAAO,KAAK,CAAC,IAAI;;;;;;kEACrE,6LAAC;wDAAI,WAAU;wDAAgE,OAAO,OAAO,KAAK,CAAC,IAAI;kEACrG,cAAA,6LAAC;4DAAK,WAAU;sEACb,OAAO,KAAK,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM5B,6LAAC;oCAAG,WAAU;oCAAoD,OAAO;wCAAE,OAAO;oCAAQ;8CACxF,cAAA,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAqC,OAAO,EAAE;;;;;;0DAC7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;8CAItB,6LAAC;oCAAG,WAAU;oCAAoD,OAAO;wCAAE,OAAO;oCAAQ;8CACxF,cAAA,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAyB,OAAO,EAAE;;;;;;0DACjD,6LAAC;gDAAI,WAAU;;oDAAsC;oDAAE,OAAO,OAAO;;;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;oCAAoD,OAAO;wCAAE,OAAO;oCAAQ;8CACxF,cAAA,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAqC,OAAO,UAAU;;;;;;0DACrE,6LAAC;gDAAI,WAAU;0DAAuC,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,MAAM;;;;;;;;;;;;;;;;;8CAGpF,6LAAC;oCAAG,WAAU;oCAA8B,OAAO;wCAAE,OAAO;oCAAQ;8CAClE,cAAA,6LAAC;wCAAI,WAAU;kDACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACrB,6LAAC,oIAAA,CAAA,UAAK;gDAAa,SAAQ;gDAAO,MAAK;0DACpC;+CADS;;;;;;;;;;;;;;;8CAMlB,6LAAC;oCAAG,WAAU;oCAAkD,OAAO;wCAAE,OAAO;oCAAO;8CACrF,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,UAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,gBAAgB,iBAAiB,OAAO,EAAE,GAAG,OAAO,OAAO,EAAE;0DAE5E,cAAA,6LAAC,6NAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;4CAGzB,iBAAiB,OAAO,EAAE,kBACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS;4DACP,gBAAgB,OAAO,EAAE;4DACzB,gBAAgB;wDAClB;wDACA,UAAU,OAAO,MAAM,KAAK;wDAC5B,WAAU;;0EAEV,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGtC,6LAAC;wDACC,SAAS;4DACP,aAAa,OAAO,EAAE;4DACtB,gBAAgB;wDAClB;wDACA,UAAU,OAAO,MAAM,KAAK;wDAC5B,WAAU;;0EAEV,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGzC,6LAAC;wDACC,SAAS;4DACP,aAAa,OAAO,EAAE;4DACtB,gBAAgB;wDAClB;wDACA,UAAU,OAAO,MAAM,KAAK;wDAC5B,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;2BA1GxC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;AAuHhC;GA1OwB;KAAA", "debugId": null}}, {"offset": {"line": 1415, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/ui/Pagination.tsx"], "sourcesContent": ["'use client'\n\nimport { ChevronLeft, ChevronRight } from 'lucide-react'\n\ninterface PaginationProps {\n  currentPage: number\n  totalPages: number\n  pageSize: number\n  totalItems: number\n  onPageChange: (page: number) => void\n  onPageSizeChange: (size: number) => void\n}\n\nexport default function Pagination({\n  currentPage,\n  totalPages,\n  pageSize,\n  totalItems,\n  onPageChange,\n  onPageSizeChange\n}: PaginationProps) {\n  const startItem = (currentPage - 1) * pageSize + 1\n  const endItem = Math.min(currentPage * pageSize, totalItems)\n\n  const getVisiblePages = () => {\n    const delta = 2\n    const range = []\n    const rangeWithDots = []\n\n    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {\n      range.push(i)\n    }\n\n    if (currentPage - delta > 2) {\n      rangeWithDots.push(1, '...')\n    } else {\n      rangeWithDots.push(1)\n    }\n\n    rangeWithDots.push(...range)\n\n    if (currentPage + delta < totalPages - 1) {\n      rangeWithDots.push('...', totalPages)\n    } else {\n      rangeWithDots.push(totalPages)\n    }\n\n    return rangeWithDots.filter((item, index, arr) => arr.indexOf(item) === index)\n  }\n\n  const visiblePages = totalPages > 1 ? getVisiblePages() : [1]\n\n  return (\n    <div className=\"flex items-center justify-between px-4 py-1.5 bg-white border-t border-gray-200\">\n      <div className=\"flex items-center text-sm text-gray-700\">\n        <span>显示</span>\n        <select\n          value={pageSize}\n          onChange={(e) => onPageSizeChange(Number(e.target.value))}\n          className=\"mx-2 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        >\n          <option value={20}>20</option>\n          <option value={50}>50</option>\n          <option value={100}>100</option>\n          <option value={200}>200</option>\n        </select>\n        <span>条，共 {totalItems} 条记录</span>\n        {totalItems > 0 && (\n          <span className=\"ml-4\">\n            第 {startItem}-{endItem} 条\n          </span>\n        )}\n      </div>\n\n      {totalPages > 1 && (\n        <div className=\"flex items-center space-x-1\">\n          <button\n            onClick={() => onPageChange(currentPage - 1)}\n            disabled={currentPage === 1}\n            className=\"px-2 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <ChevronLeft className=\"h-4 w-4\" />\n          </button>\n\n          {visiblePages.map((page, index) => (\n            <button\n              key={index}\n              onClick={() => typeof page === 'number' && onPageChange(page)}\n              disabled={page === '...'}\n              className={`px-3 py-1 text-sm rounded ${\n                page === currentPage\n                  ? 'bg-blue-600 text-white'\n                  : page === '...'\n                  ? 'text-gray-400 cursor-default'\n                  : 'text-gray-700 hover:bg-gray-100'\n              }`}\n            >\n              {page}\n            </button>\n          ))}\n\n          <button\n            onClick={() => onPageChange(currentPage + 1)}\n            disabled={currentPage === totalPages}\n            className=\"px-2 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <ChevronRight className=\"h-4 w-4\" />\n          </button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAae,SAAS,WAAW,EACjC,WAAW,EACX,UAAU,EACV,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,gBAAgB,EACA;IAChB,MAAM,YAAY,CAAC,cAAc,CAAC,IAAI,WAAW;IACjD,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,UAAU;IAEjD,MAAM,kBAAkB;QACtB,MAAM,QAAQ;QACd,MAAM,QAAQ,EAAE;QAChB,MAAM,gBAAgB,EAAE;QAExB,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,cAAc,QAAQ,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,QAAQ,IAAK;YACtG,MAAM,IAAI,CAAC;QACb;QAEA,IAAI,cAAc,QAAQ,GAAG;YAC3B,cAAc,IAAI,CAAC,GAAG;QACxB,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QAEA,cAAc,IAAI,IAAI;QAEtB,IAAI,cAAc,QAAQ,aAAa,GAAG;YACxC,cAAc,IAAI,CAAC,OAAO;QAC5B,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QAEA,OAAO,cAAc,MAAM,CAAC,CAAC,MAAM,OAAO,MAAQ,IAAI,OAAO,CAAC,UAAU;IAC1E;IAEA,MAAM,eAAe,aAAa,IAAI,oBAAoB;QAAC;KAAE;IAE7D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAK;;;;;;kCACN,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;wBACvD,WAAU;;0CAEV,6LAAC;gCAAO,OAAO;0CAAI;;;;;;0CACnB,6LAAC;gCAAO,OAAO;0CAAI;;;;;;0CACnB,6LAAC;gCAAO,OAAO;0CAAK;;;;;;0CACpB,6LAAC;gCAAO,OAAO;0CAAK;;;;;;;;;;;;kCAEtB,6LAAC;;4BAAK;4BAAK;4BAAW;;;;;;;oBACrB,aAAa,mBACZ,6LAAC;wBAAK,WAAU;;4BAAO;4BAClB;4BAAU;4BAAE;4BAAQ;;;;;;;;;;;;;YAK5B,aAAa,mBACZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,aAAa,cAAc;wBAC1C,UAAU,gBAAgB;wBAC1B,WAAU;kCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;oBAGxB,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;4BAEC,SAAS,IAAM,OAAO,SAAS,YAAY,aAAa;4BACxD,UAAU,SAAS;4BACnB,WAAW,CAAC,0BAA0B,EACpC,SAAS,cACL,2BACA,SAAS,QACT,iCACA,mCACJ;sCAED;2BAXI;;;;;kCAeT,6LAAC;wBACC,SAAS,IAAM,aAAa,cAAc;wBAC1C,UAAU,gBAAgB;wBAC1B,WAAU;kCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMpC;KAnGwB", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Client } from '@/types'\nimport ClientFilters from '@/components/clients/ClientFilters'\nimport ClientTable from '@/components/clients/ClientTable'\nimport Pagination from '@/components/ui/Pagination'\n\n// 模拟数据\nconst mockClients: Client[] = [\n  {\n    id: 'client-001',\n    name: 'PC-办公室-001',\n    ip: '*************',\n    location: '北京市 海淀区',\n    status: 'online',\n    owner: {\n      name: '050613',\n      uuid: 'a5d368211bec18f3f43aa94492702345'\n    },\n    os: 'Windows 11 Pro',\n    version: '1.2.3',\n    lastSeen: '2024-01-15 14:30:00',\n    lastOnline: '2025-06-17',\n    uptime: 86400,\n    tags: ['办公', '生产环境'],\n    group: 'production',\n    cpu: 45,\n    memory: 68,\n    disk: 75,\n    network: { upload: 1024, download: 2048 }\n  },\n  {\n    id: 'client-002',\n    name: 'PC-开发-002',\n    ip: '*************',\n    location: '上海市 浦东新区',\n    status: 'online',\n    owner: {\n      name: '051201',\n      uuid: 'b7f4c9e2d8a6b3f1c5e9a2d7b4f8c1e6'\n    },\n    os: 'Ubuntu 22.04',\n    version: '1.2.3',\n    lastSeen: '2024-01-15 14:25:00',\n    lastOnline: '2025-06-17',\n    uptime: 172800,\n    tags: ['开发', '测试环境'],\n    group: 'development',\n    cpu: 32,\n    memory: 54,\n    disk: 45,\n    network: { upload: 512, download: 1024 }\n  },\n  {\n    id: 'client-003',\n    name: 'PC-会议室-003',\n    ip: '*************',\n    location: '广州市 天河区',\n    status: 'offline',\n    owner: {\n      name: '052408',\n      uuid: 'c9e3f7a1d5b8c2f6e0a4d8b2f6c0e4a8'\n    },\n    os: 'Windows 10 Pro',\n    version: '1.2.2',\n    lastSeen: '2024-01-15 12:00:00',\n    lastOnline: '2025-06-16',\n    uptime: 0,\n    tags: ['会议室'],\n    group: 'production',\n    cpu: 0,\n    memory: 0,\n    disk: 60,\n    network: { upload: 0, download: 0 }\n  },\n  {\n    id: 'client-004',\n    name: 'PC-财务-004',\n    ip: '*************',\n    location: '深圳市 南山区',\n    status: 'online',\n    owner: {\n      name: '060815',\n      uuid: 'd2f8b4e6c0a3f7d1b5e9c3f7a1d5b9c3'\n    },\n    os: 'Windows 11 Pro',\n    version: '1.2.4',\n    lastSeen: '2024-01-15 15:00:00',\n    lastOnline: '2025-06-17',\n    uptime: 129600,\n    tags: ['财务', '生产环境'],\n    group: 'production',\n    cpu: 38,\n    memory: 72,\n    disk: 82,\n    network: { upload: 768, download: 1536 }\n  },\n  {\n    id: 'client-005',\n    name: 'PC-人事-005',\n    ip: '*************',\n    location: '杭州市 西湖区',\n    status: 'connecting',\n    owner: {\n      name: '071022',\n      uuid: 'e4a8d2f6b0c4e8a2d6f0b4c8e2a6d0f4'\n    },\n    os: 'Windows 10 Pro',\n    version: '1.2.1',\n    lastSeen: '2024-01-15 13:45:00',\n    lastOnline: '2025-06-17',\n    uptime: 43200,\n    tags: ['人事', '办公'],\n    group: 'production',\n    cpu: 25,\n    memory: 45,\n    disk: 65,\n    network: { upload: 256, download: 512 }\n  },\n  {\n    id: 'client-006',\n    name: 'PC-测试-006',\n    ip: '*************',\n    location: '成都市 高新区',\n    status: 'online',\n    owner: {\n      name: '081229',\n      uuid: 'f6c0e4a8d2f6b0c4e8a2d6f0b4c8e2a6'\n    },\n    os: 'Ubuntu 20.04',\n    version: '1.1.9',\n    lastSeen: '2024-01-15 16:20:00',\n    lastOnline: '2025-06-17',\n    uptime: 259200,\n    tags: ['测试', '开发环境'],\n    group: 'development',\n    cpu: 55,\n    memory: 78,\n    disk: 45,\n    network: { upload: 1024, download: 2048 }\n  },\n  {\n    id: 'client-007',\n    name: 'PC-销售-007',\n    ip: '*************',\n    location: '武汉市 江汉区',\n    status: 'offline',\n    owner: {\n      name: '090406',\n      uuid: 'a8d2f6b0c4e8a2d6f0b4c8e2a6d0f4c8'\n    },\n    os: 'Windows 11 Pro',\n    version: '1.2.3',\n    lastSeen: '2024-01-15 11:30:00',\n    lastOnline: '2025-06-16',\n    uptime: 0,\n    tags: ['销售', '客户管理'],\n    group: 'production',\n    cpu: 0,\n    memory: 0,\n    disk: 78,\n    network: { upload: 0, download: 0 }\n  },\n  {\n    id: 'client-008',\n    name: 'PC-设计-008',\n    ip: '*************',\n    location: '西安市 雁塔区',\n    status: 'online',\n    owner: {\n      name: '100513',\n      uuid: 'b0c4e8a2d6f0b4c8e2a6d0f4c8e2a6d0'\n    },\n    os: 'macOS Ventura',\n    version: '1.2.2',\n    lastSeen: '2024-01-15 14:15:00',\n    lastOnline: '2025-06-17',\n    uptime: 345600,\n    tags: ['设计', '创意'],\n    group: 'production',\n    cpu: 62,\n    memory: 85,\n    disk: 92,\n    network: { upload: 2048, download: 4096 }\n  },\n  {\n    id: 'client-009',\n    name: 'PC-运维-009',\n    ip: '*************',\n    location: '南京市 建邺区',\n    status: 'online',\n    owner: {\n      name: '111720',\n      uuid: 'c4e8a2d6f0b4c8e2a6d0f4c8e2a6d0f4'\n    },\n    os: 'CentOS 8',\n    version: '1.2.5',\n    lastSeen: '2024-01-15 15:30:00',\n    lastOnline: '2025-06-17',\n    uptime: 604800,\n    tags: ['运维', '服务器'],\n    group: 'production',\n    cpu: 28,\n    memory: 42,\n    disk: 35,\n    network: { upload: 512, download: 1024 }\n  },\n  {\n    id: 'client-010',\n    name: 'PC-客服-010',\n    ip: '*************',\n    location: '青岛市 市南区',\n    status: 'connecting',\n    owner: {\n      name: '120927',\n      uuid: 'e8a2d6f0b4c8e2a6d0f4c8e2a6d0f4c8'\n    },\n    os: 'Windows 10 Pro',\n    version: '1.2.0',\n    lastSeen: '2024-01-15 12:45:00',\n    lastOnline: '2025-06-17',\n    uptime: 21600,\n    tags: ['客服', '支持'],\n    group: 'production',\n    cpu: 18,\n    memory: 35,\n    disk: 58,\n    network: { upload: 128, download: 256 }\n  },\n  {\n    id: 'client-011',\n    name: 'PC-市场-011',\n    ip: '*************',\n    location: '天津市 和平区',\n    status: 'online',\n    owner: {\n      name: '130104',\n      uuid: 'f0b4c8e2a6d0f4c8e2a6d0f4c8e2a6d0'\n    },\n    os: 'Windows 11 Pro',\n    version: '1.2.3',\n    lastSeen: '2024-01-15 14:50:00',\n    lastOnline: '2025-06-17',\n    uptime: 518400,\n    tags: ['市场', '推广'],\n    group: 'production',\n    cpu: 42,\n    memory: 58,\n    disk: 71,\n    network: { upload: 512, download: 1024 }\n  },\n  {\n    id: 'client-012',\n    name: 'PC-法务-012',\n    ip: '*************',\n    location: '重庆市 渝中区',\n    status: 'offline',\n    owner: {\n      name: '140311',\n      uuid: 'a2d6f0b4c8e2a6d0f4c8e2a6d0f4c8e2'\n    },\n    os: 'Windows 10 Pro',\n    version: '1.1.8',\n    lastSeen: '2024-01-15 10:20:00',\n    lastOnline: '2025-06-15',\n    uptime: 0,\n    tags: ['法务', '合规'],\n    group: 'production',\n    cpu: 0,\n    memory: 0,\n    disk: 55,\n    network: { upload: 0, download: 0 }\n  },\n  {\n    id: 'client-013',\n    name: 'PC-产品-013',\n    ip: '*************',\n    location: '苏州市 工业园区',\n    status: 'online',\n    owner: {\n      name: '150518',\n      uuid: 'b4c8e2a6d0f4c8e2a6d0f4c8e2a6d0f4'\n    },\n    os: 'macOS Monterey',\n    version: '1.2.1',\n    lastSeen: '2024-01-15 16:10:00',\n    lastOnline: '2025-06-17',\n    uptime: 432000,\n    tags: ['产品', '规划'],\n    group: 'production',\n    cpu: 48,\n    memory: 76,\n    disk: 88,\n    network: { upload: 1024, download: 2048 }\n  },\n  {\n    id: 'client-014',\n    name: 'PC-质量-014',\n    ip: '*************',\n    location: '长沙市 岳麓区',\n    status: 'connecting',\n    owner: {\n      name: '160725',\n      uuid: 'c8e2a6d0f4c8e2a6d0f4c8e2a6d0f4c8'\n    },\n    os: 'Ubuntu 22.04',\n    version: '1.2.4',\n    lastSeen: '2024-01-15 13:25:00',\n    lastOnline: '2025-06-17',\n    uptime: 86400,\n    tags: ['质量', '测试'],\n    group: 'development',\n    cpu: 35,\n    memory: 52,\n    disk: 42,\n    network: { upload: 256, download: 512 }\n  },\n  {\n    id: 'client-015',\n    name: 'PC-采购-015',\n    ip: '*************',\n    location: '郑州市 金水区',\n    status: 'online',\n    owner: {\n      name: '170901',\n      uuid: 'e2a6d0f4c8e2a6d0f4c8e2a6d0f4c8e2'\n    },\n    os: 'Windows 11 Pro',\n    version: '1.2.2',\n    lastSeen: '2024-01-15 15:40:00',\n    lastOnline: '2025-06-17',\n    uptime: 302400,\n    tags: ['采购', '供应链'],\n    group: 'production',\n    cpu: 31,\n    memory: 48,\n    disk: 67,\n    network: { upload: 384, download: 768 }\n  },\n  {\n    id: 'client-016',\n    name: 'PC-物流-016',\n    ip: '*************',\n    location: '济南市 历下区',\n    status: 'offline',\n    owner: {\n      name: '181108',\n      uuid: 'f4c8e2a6d0f4c8e2a6d0f4c8e2a6d0f4'\n    },\n    os: 'Windows 10 Pro',\n    version: '1.1.9',\n    lastSeen: '2024-01-15 09:15:00',\n    lastOnline: '2025-06-15',\n    uptime: 0,\n    tags: ['物流', '仓储'],\n    group: 'production',\n    cpu: 0,\n    memory: 0,\n    disk: 73,\n    network: { upload: 0, download: 0 }\n  },\n  {\n    id: 'client-017',\n    name: 'PC-安全-017',\n    ip: '*************',\n    location: '福州市 鼓楼区',\n    status: 'online',\n    owner: {\n      name: '191215',\n      uuid: 'a6d0f4c8e2a6d0f4c8e2a6d0f4c8e2a6'\n    },\n    os: 'CentOS 7',\n    version: '1.2.5',\n    lastSeen: '2024-01-15 16:00:00',\n    lastOnline: '2025-06-17',\n    uptime: 1209600,\n    tags: ['安全', '监控'],\n    group: 'production',\n    cpu: 22,\n    memory: 38,\n    disk: 28,\n    network: { upload: 256, download: 512 }\n  },\n  {\n    id: 'client-018',\n    name: 'PC-培训-018',\n    ip: '*************',\n    location: '石家庄市 长安区',\n    status: 'connecting',\n    owner: {\n      name: '200422',\n      uuid: 'd0f4c8e2a6d0f4c8e2a6d0f4c8e2a6d0'\n    },\n    os: 'Windows 11 Pro',\n    version: '1.2.0',\n    lastSeen: '2024-01-15 12:30:00',\n    lastOnline: '2025-06-17',\n    uptime: 64800,\n    tags: ['培训', '教育'],\n    group: 'production',\n    cpu: 15,\n    memory: 28,\n    disk: 45,\n    network: { upload: 128, download: 256 }\n  },\n  {\n    id: 'client-019',\n    name: 'PC-档案-019',\n    ip: '*************',\n    location: '合肥市 蜀山区',\n    status: 'online',\n    owner: {\n      name: '210629',\n      uuid: 'c2a6d0f4c8e2a6d0f4c8e2a6d0f4c8e2'\n    },\n    os: 'Windows 10 Pro',\n    version: '1.1.7',\n    lastSeen: '2024-01-15 14:05:00',\n    lastOnline: '2025-06-17',\n    uptime: 777600,\n    tags: ['档案', '文档'],\n    group: 'production',\n    cpu: 12,\n    memory: 25,\n    disk: 89,\n    network: { upload: 64, download: 128 }\n  },\n  {\n    id: 'client-020',\n    name: 'PC-备份-020',\n    ip: '*************',\n    location: '南昌市 东湖区',\n    status: 'offline',\n    owner: {\n      name: '220806',\n      uuid: 'e4a8d2f6b0c4e8a2d6f0b4c8e2a6d0f4'\n    },\n    os: 'Ubuntu 18.04',\n    version: '1.0.9',\n    lastSeen: '2024-01-15 08:45:00',\n    lastOnline: '2025-06-14',\n    uptime: 0,\n    tags: ['备份', '存储'],\n    group: 'production',\n    cpu: 0,\n    memory: 0,\n    disk: 95,\n    network: { upload: 0, download: 0 }\n  }\n]\n\nexport default function Home() {\n  const [clients] = useState<Client[]>(mockClients)\n  const [selectedClients, setSelectedClients] = useState<string[]>([])\n  const [searchQuery, setSearchQuery] = useState('')\n  const [statusFilter, setStatusFilter] = useState<'all' | 'online' | 'offline' | 'connecting'>('all')\n  const [groupFilter, setGroupFilter] = useState('all')\n  const [currentPage, setCurrentPage] = useState(1)\n  const [pageSize, setPageSize] = useState(20)\n\n  // 筛选客户端\n  const filteredClients = clients.filter(client => {\n    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         client.ip.includes(searchQuery)\n    const matchesStatus = statusFilter === 'all' || client.status === statusFilter\n    const matchesGroup = groupFilter === 'all' || client.group === groupFilter\n\n    return matchesSearch && matchesStatus && matchesGroup\n  })\n\n  // 分页计算\n  const totalPages = Math.ceil(filteredClients.length / pageSize)\n  const startIndex = (currentPage - 1) * pageSize\n  const endIndex = startIndex + pageSize\n  const paginatedClients = filteredClients.slice(startIndex, endIndex)\n\n  // 当筛选条件改变时重置到第一页\n  useEffect(() => {\n    setCurrentPage(1)\n  }, [searchQuery, statusFilter, groupFilter])\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page)\n  }\n\n  const handlePageSizeChange = (size: number) => {\n    setPageSize(size)\n    setCurrentPage(1)\n  }\n\n  const handleSelectClient = (clientId: string) => {\n    setSelectedClients(prev =>\n      prev.includes(clientId)\n        ? prev.filter(id => id !== clientId)\n        : [...prev, clientId]\n    )\n  }\n\n  const handleSelectAll = (selected: boolean) => {\n    setSelectedClients(selected ? paginatedClients.map(c => c.id) : [])\n  }\n\n  const handleRemoteConnect = (clientId: string) => {\n    console.log('连接到客户端:', clientId)\n    // TODO: 实现远程连接逻辑\n  }\n\n  const handleFileManage = (clientId: string) => {\n    console.log('文件管理:', clientId)\n    // TODO: 实现文件管理逻辑\n  }\n\n  const handleCmdExecute = (clientId: string) => {\n    console.log('CMD命令:', clientId)\n    // TODO: 实现CMD命令逻辑\n  }\n\n  const handleRefresh = () => {\n    console.log('刷新客户端列表')\n    // TODO: 实现刷新逻辑\n  }\n\n  const handleExport = () => {\n    console.log('导出客户端列表')\n    // TODO: 实现导出逻辑\n  }\n\n  const handleBatchConnect = () => {\n    console.log('批量连接客户端:', selectedClients)\n    // TODO: 实现批量连接逻辑\n  }\n\n  const handleAddClient = () => {\n    console.log('添加新客户端')\n    // TODO: 实现添加客户端逻辑\n  }\n\n  return (\n    <div className=\"space-y-4 animate-in fade-in duration-500\">\n      <ClientFilters\n        searchQuery={searchQuery}\n        onSearchChange={setSearchQuery}\n        statusFilter={statusFilter}\n        onStatusFilterChange={setStatusFilter}\n        groupFilter={groupFilter}\n        onGroupFilterChange={setGroupFilter}\n        selectedCount={selectedClients.length}\n        onRefresh={handleRefresh}\n        onExport={handleExport}\n        onBatchConnect={handleBatchConnect}\n        onAddClient={handleAddClient}\n      />\n\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        <ClientTable\n          clients={paginatedClients}\n          selectedClients={selectedClients}\n          onSelectClient={handleSelectClient}\n          onSelectAll={handleSelectAll}\n          onRemoteConnect={handleRemoteConnect}\n          onFileManage={handleFileManage}\n          onCmdExecute={handleCmdExecute}\n        />\n\n        <Pagination\n          currentPage={currentPage}\n          totalPages={totalPages}\n          pageSize={pageSize}\n          totalItems={filteredClients.length}\n          onPageChange={handlePageChange}\n          onPageSizeChange={handlePageSizeChange}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;;;AANA;;;;;AAQA,OAAO;AACP,MAAM,cAAwB;IAC5B;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAO;QACpB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAM,UAAU;QAAK;IAC1C;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAO;QACpB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAK;IACzC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;SAAM;QACb,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAG,UAAU;QAAE;IACpC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAO;QACpB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAK;IACzC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAI;IACxC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAO;QACpB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAM,UAAU;QAAK;IAC1C;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAO;QACpB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAG,UAAU;QAAE;IACpC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAM,UAAU;QAAK;IAC1C;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAM;QACnB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAK;IACzC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAI;IACxC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAK;IACzC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAG,UAAU;QAAE;IACpC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAM,UAAU;QAAK;IAC1C;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAI;IACxC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAM;QACnB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAI;IACxC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAG,UAAU;QAAE;IACpC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAI;IACxC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAI;IACxC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAI,UAAU;QAAI;IACvC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,IAAI;QACJ,SAAS;QACT,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;YAAC;YAAM;SAAK;QAClB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAG,UAAU;QAAE;IACpC;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C;IAC9F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,QAAQ;IACR,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,OAAO,EAAE,CAAC,QAAQ,CAAC;QACxC,MAAM,gBAAgB,iBAAiB,SAAS,OAAO,MAAM,KAAK;QAClE,MAAM,eAAe,gBAAgB,SAAS,OAAO,KAAK,KAAK;QAE/D,OAAO,iBAAiB,iBAAiB;IAC3C;IAEA,OAAO;IACP,MAAM,aAAa,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG;IACtD,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAC9B,MAAM,mBAAmB,gBAAgB,KAAK,CAAC,YAAY;IAE3D,iBAAiB;IACjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,eAAe;QACjB;yBAAG;QAAC;QAAa;QAAc;KAAY;IAE3C,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB,CAAA,OACjB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,YACzB;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,kBAAkB,CAAC;QACvB,mBAAmB,WAAW,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,IAAI,EAAE;IACpE;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,WAAW;IACvB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,SAAS;IACrB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,UAAU;IACtB,kBAAkB;IACpB;IAEA,MAAM,gBAAgB;QACpB,QAAQ,GAAG,CAAC;IACZ,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC;IACZ,eAAe;IACjB;IAEA,MAAM,qBAAqB;QACzB,QAAQ,GAAG,CAAC,YAAY;IACxB,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC;IACZ,kBAAkB;IACpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,iJAAA,CAAA,UAAa;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,sBAAsB;gBACtB,aAAa;gBACb,qBAAqB;gBACrB,eAAe,gBAAgB,MAAM;gBACrC,WAAW;gBACX,UAAU;gBACV,gBAAgB;gBAChB,aAAa;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+IAAA,CAAA,UAAW;wBACV,SAAS;wBACT,iBAAiB;wBACjB,gBAAgB;wBAChB,aAAa;wBACb,iBAAiB;wBACjB,cAAc;wBACd,cAAc;;;;;;kCAGhB,6LAAC,yIAAA,CAAA,UAAU;wBACT,aAAa;wBACb,YAAY;wBACZ,UAAU;wBACV,YAAY,gBAAgB,MAAM;wBAClC,cAAc;wBACd,kBAAkB;;;;;;;;;;;;;;;;;;AAK5B;GA5HwB;KAAA", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/monitoring/MetricCard.tsx"], "sourcesContent": ["import { ReactNode } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface MetricCardProps {\n  title: string\n  value: string | number\n  unit?: string\n  change?: number\n  changeLabel?: string\n  icon: ReactNode\n  color?: 'blue' | 'green' | 'yellow' | 'red' | 'gray'\n  children?: ReactNode\n}\n\nexport default function MetricCard({\n  title,\n  value,\n  unit,\n  change,\n  changeLabel,\n  icon,\n  color = 'blue',\n  children\n}: MetricCardProps) {\n  const colorClasses = {\n    blue: 'bg-blue-50 text-blue-600 border-blue-200',\n    green: 'bg-green-50 text-green-600 border-green-200',\n    yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',\n    red: 'bg-red-50 text-red-600 border-red-200',\n    gray: 'bg-gray-50 text-gray-600 border-gray-200'\n  }\n\n  const getChangeColor = (change: number) => {\n    if (change > 0) return 'text-red-600'\n    if (change < 0) return 'text-green-600'\n    return 'text-gray-600'\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow border border-gray-200 p-6 hover-lift\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className={cn(\n          'p-3 rounded-lg border',\n          colorClasses[color]\n        )}>\n          {icon}\n        </div>\n        {change !== undefined && (\n          <div className={cn('text-sm font-medium', getChangeColor(change))}>\n            {change > 0 ? '+' : ''}{change}%\n            {changeLabel && <span className=\"text-gray-500 ml-1\">{changeLabel}</span>}\n          </div>\n        )}\n      </div>\n      \n      <div className=\"mb-2\">\n        <h3 className=\"text-sm font-medium text-gray-600\">{title}</h3>\n        <div className=\"flex items-baseline space-x-1\">\n          <span className=\"text-2xl font-bold text-gray-900\">{value}</span>\n          {unit && <span className=\"text-sm text-gray-500\">{unit}</span>}\n        </div>\n      </div>\n      \n      {children}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAae,SAAS,WAAW,EACjC,KAAK,EACL,KAAK,EACL,IAAI,EACJ,MAAM,EACN,WAAW,EACX,IAAI,EACJ,QAAQ,MAAM,EACd,QAAQ,EACQ;IAChB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,KAAK;QACL,MAAM;IACR;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,yBACA,YAAY,CAAC,MAAM;kCAElB;;;;;;oBAEF,WAAW,2BACV,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB,eAAe;;4BACtD,SAAS,IAAI,MAAM;4BAAI;4BAAO;4BAC9B,6BAAe,8OAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;0BAK5D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAoC;;;;;;4BACnD,sBAAQ,8OAAC;gCAAK,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;YAIrD;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/monitoring/ProgressBar.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface ProgressBarProps {\n  value: number\n  max?: number\n  size?: 'sm' | 'md' | 'lg'\n  color?: 'blue' | 'green' | 'yellow' | 'red'\n  showLabel?: boolean\n  label?: string\n}\n\nexport default function ProgressBar({\n  value,\n  max = 100,\n  size = 'md',\n  color = 'blue',\n  showLabel = true,\n  label\n}: ProgressBarProps) {\n  const percentage = Math.min((value / max) * 100, 100)\n  \n  const sizeClasses = {\n    sm: 'h-2',\n    md: 'h-3',\n    lg: 'h-4'\n  }\n  \n  const colorClasses = {\n    blue: 'bg-blue-500',\n    green: 'bg-green-500',\n    yellow: 'bg-yellow-500',\n    red: 'bg-red-500'\n  }\n  \n  const getColorByPercentage = (percentage: number) => {\n    if (percentage >= 90) return 'red'\n    if (percentage >= 75) return 'yellow'\n    if (percentage >= 50) return 'blue'\n    return 'green'\n  }\n  \n  const autoColor = getColorByPercentage(percentage)\n  const finalColor = color === 'blue' ? autoColor : color\n\n  return (\n    <div className=\"w-full\">\n      {showLabel && (\n        <div className=\"flex justify-between items-center mb-1\">\n          <span className=\"text-sm text-gray-600\">{label}</span>\n          <span className=\"text-sm font-medium text-gray-900\">\n            {value}{max === 100 ? '%' : `/${max}`}\n          </span>\n        </div>\n      )}\n      <div className={cn('w-full bg-gray-200 rounded-full', sizeClasses[size])}>\n        <div\n          className={cn(\n            'rounded-full transition-all duration-300',\n            sizeClasses[size],\n            colorClasses[finalColor]\n          )}\n          style={{ width: `${percentage}%` }}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAWe,SAAS,YAAY,EAClC,KAAK,EACL,MAAM,GAAG,EACT,OAAO,IAAI,EACX,QAAQ,MAAM,EACd,YAAY,IAAI,EAChB,KAAK,EACY;IACjB,MAAM,aAAa,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK;IAEjD,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,KAAK;IACP;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,IAAI,cAAc,IAAI,OAAO;QAC7B,OAAO;IACT;IAEA,MAAM,YAAY,qBAAqB;IACvC,MAAM,aAAa,UAAU,SAAS,YAAY;IAElD,qBACE,8OAAC;QAAI,WAAU;;YACZ,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAyB;;;;;;kCACzC,8OAAC;wBAAK,WAAU;;4BACb;4BAAO,QAAQ,MAAM,MAAM,CAAC,CAAC,EAAE,KAAK;;;;;;;;;;;;;0BAI3C,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC,WAAW,CAAC,KAAK;0BACrE,cAAA,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4CACA,WAAW,CAAC,KAAK,EACjB,YAAY,CAAC,WAAW;oBAE1B,OAAO;wBAAE,OAAO,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/app/monitoring/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { \n  Cpu, \n  MemoryStick, \n  HardDrive, \n  Wifi, \n  Monitor,\n  Clock,\n  Thermometer,\n  Activity\n} from 'lucide-react'\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts'\nimport MetricCard from '@/components/monitoring/MetricCard'\nimport ProgressBar from '@/components/monitoring/ProgressBar'\nimport { formatBytes } from '@/lib/utils'\n\n// 模拟实时数据\nconst generateTimeSeriesData = (points: number = 20) => {\n  const data = []\n  const now = new Date()\n  \n  for (let i = points - 1; i >= 0; i--) {\n    const time = new Date(now.getTime() - i * 30000) // 每30秒一个点\n    data.push({\n      time: time.toLocaleTimeString(),\n      cpu: Math.random() * 100,\n      memory: Math.random() * 100,\n      network: Math.random() * 1000,\n      disk: Math.random() * 100\n    })\n  }\n  \n  return data\n}\n\nexport default function MonitoringPage() {\n  const [selectedClient, setSelectedClient] = useState('client-001')\n  const [timeSeriesData, setTimeSeriesData] = useState(generateTimeSeriesData())\n  const [currentMetrics, setCurrentMetrics] = useState({\n    cpu: { usage: 45, cores: 8, model: 'Intel Core i7-12700K', temperature: 65 },\n    memory: { total: 32 * 1024 * 1024 * 1024, used: 22 * 1024 * 1024 * 1024, available: 10 * 1024 * 1024 * 1024 },\n    disk: { total: 1024 * 1024 * 1024 * 1024, used: 768 * 1024 * 1024 * 1024, available: 256 * 1024 * 1024 * 1024 },\n    network: { upload: 1024, download: 2048 },\n    uptime: 86400 * 3 + 3600 * 5 + 60 * 30\n  })\n\n  const clients = [\n    { id: 'client-001', name: 'PC-办公室-001', status: 'online' },\n    { id: 'client-002', name: 'PC-开发-002', status: 'online' },\n    { id: 'client-003', name: 'PC-会议室-003', status: 'offline' }\n  ]\n\n  // 模拟实时数据更新\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setTimeSeriesData(prev => {\n        const newData = [...prev.slice(1)]\n        const now = new Date()\n        newData.push({\n          time: now.toLocaleTimeString(),\n          cpu: Math.random() * 100,\n          memory: Math.random() * 100,\n          network: Math.random() * 1000,\n          disk: Math.random() * 100\n        })\n        return newData\n      })\n      \n      // 更新当前指标\n      setCurrentMetrics(prev => ({\n        ...prev,\n        cpu: { ...prev.cpu, usage: Math.random() * 100, temperature: 60 + Math.random() * 20 },\n        network: { upload: Math.random() * 2048, download: Math.random() * 4096 }\n      }))\n    }, 30000) // 每30秒更新一次\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const formatUptime = (seconds: number) => {\n    const days = Math.floor(seconds / 86400)\n    const hours = Math.floor((seconds % 86400) / 3600)\n    const minutes = Math.floor((seconds % 3600) / 60)\n    return `${days}天 ${hours}小时 ${minutes}分钟`\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 顶部选择器 */}\n      <div className=\"bg-white rounded-lg shadow p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-xl font-semibold text-gray-900\">系统资源监控</h1>\n            <p className=\"text-sm text-gray-500 mt-1\">实时监控客户端系统资源使用情况</p>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <select\n              value={selectedClient}\n              onChange={(e) => setSelectedClient(e.target.value)}\n              className=\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white\"\n            >\n              {clients.map(client => (\n                <option key={client.id} value={client.id}>\n                  {client.name} ({client.status === 'online' ? '在线' : '离线'})\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* 关键指标卡片 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <MetricCard\n          title=\"CPU 使用率\"\n          value={currentMetrics.cpu.usage.toFixed(1)}\n          unit=\"%\"\n          change={2.5}\n          changeLabel=\"vs 上小时\"\n          icon={<Cpu className=\"h-6 w-6\" />}\n          color={currentMetrics.cpu.usage > 80 ? 'red' : currentMetrics.cpu.usage > 60 ? 'yellow' : 'green'}\n        >\n          <ProgressBar value={currentMetrics.cpu.usage} />\n          <div className=\"mt-2 text-xs text-gray-500\">\n            {currentMetrics.cpu.cores} 核心 • {currentMetrics.cpu.temperature}°C\n          </div>\n        </MetricCard>\n\n        <MetricCard\n          title=\"内存使用\"\n          value={((currentMetrics.memory.used / currentMetrics.memory.total) * 100).toFixed(1)}\n          unit=\"%\"\n          change={-1.2}\n          changeLabel=\"vs 上小时\"\n          icon={<MemoryStick className=\"h-6 w-6\" />}\n          color=\"blue\"\n        >\n          <ProgressBar value={(currentMetrics.memory.used / currentMetrics.memory.total) * 100} />\n          <div className=\"mt-2 text-xs text-gray-500\">\n            {formatBytes(currentMetrics.memory.used)} / {formatBytes(currentMetrics.memory.total)}\n          </div>\n        </MetricCard>\n\n        <MetricCard\n          title=\"磁盘使用\"\n          value={((currentMetrics.disk.used / currentMetrics.disk.total) * 100).toFixed(1)}\n          unit=\"%\"\n          change={0.8}\n          changeLabel=\"vs 昨天\"\n          icon={<HardDrive className=\"h-6 w-6\" />}\n          color=\"yellow\"\n        >\n          <ProgressBar value={(currentMetrics.disk.used / currentMetrics.disk.total) * 100} />\n          <div className=\"mt-2 text-xs text-gray-500\">\n            {formatBytes(currentMetrics.disk.available)} 可用\n          </div>\n        </MetricCard>\n\n        <MetricCard\n          title=\"网络流量\"\n          value={formatBytes(currentMetrics.network.download + currentMetrics.network.upload)}\n          unit=\"/s\"\n          change={15.3}\n          changeLabel=\"vs 上小时\"\n          icon={<Wifi className=\"h-6 w-6\" />}\n          color=\"green\"\n        >\n          <div className=\"mt-2 space-y-1\">\n            <div className=\"flex justify-between text-xs\">\n              <span className=\"text-gray-500\">下载</span>\n              <span className=\"font-medium\">{formatBytes(currentMetrics.network.download)}/s</span>\n            </div>\n            <div className=\"flex justify-between text-xs\">\n              <span className=\"text-gray-500\">上传</span>\n              <span className=\"font-medium\">{formatBytes(currentMetrics.network.upload)}/s</span>\n            </div>\n          </div>\n        </MetricCard>\n      </div>\n\n      {/* 图表区域 */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* CPU 和内存趋势 */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">CPU & 内存使用趋势</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <LineChart data={timeSeriesData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"time\" />\n              <YAxis />\n              <Tooltip />\n              <Line \n                type=\"monotone\" \n                dataKey=\"cpu\" \n                stroke=\"#3b82f6\" \n                strokeWidth={2}\n                name=\"CPU (%)\"\n              />\n              <Line \n                type=\"monotone\" \n                dataKey=\"memory\" \n                stroke=\"#10b981\" \n                strokeWidth={2}\n                name=\"内存 (%)\"\n              />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* 网络流量趋势 */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">网络流量趋势</h3>\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <AreaChart data={timeSeriesData}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis dataKey=\"time\" />\n              <YAxis />\n              <Tooltip />\n              <Area \n                type=\"monotone\" \n                dataKey=\"network\" \n                stroke=\"#8b5cf6\" \n                fill=\"#8b5cf6\" \n                fillOpacity={0.3}\n                name=\"网络 (KB/s)\"\n              />\n            </AreaChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* 系统信息 */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* 系统详情 */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">系统信息</h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-500\">操作系统</span>\n              <span className=\"text-sm font-medium\">Windows 11 Pro</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-500\">处理器</span>\n              <span className=\"text-sm font-medium\">{currentMetrics.cpu.model}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-500\">运行时间</span>\n              <span className=\"text-sm font-medium\">{formatUptime(currentMetrics.uptime)}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-500\">最后更新</span>\n              <span className=\"text-sm font-medium\">{new Date().toLocaleString()}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* 进程信息 */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">热门进程</h3>\n          <div className=\"space-y-3\">\n            {[\n              { name: 'chrome.exe', cpu: 15.2, memory: 1024 },\n              { name: 'code.exe', cpu: 8.7, memory: 512 },\n              { name: 'node.exe', cpu: 5.3, memory: 256 },\n              { name: 'explorer.exe', cpu: 2.1, memory: 128 }\n            ].map((process, index) => (\n              <div key={index} className=\"flex items-center justify-between\">\n                <div>\n                  <div className=\"text-sm font-medium\">{process.name}</div>\n                  <div className=\"text-xs text-gray-500\">{formatBytes(process.memory * 1024 * 1024)}</div>\n                </div>\n                <div className=\"text-sm font-medium\">{process.cpu}%</div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* 警告和通知 */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">系统警告</h3>\n          <div className=\"space-y-3\">\n            <div className=\"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n              <div className=\"flex items-center\">\n                <Thermometer className=\"h-4 w-4 text-yellow-600 mr-2\" />\n                <span className=\"text-sm font-medium text-yellow-800\">CPU 温度偏高</span>\n              </div>\n              <p className=\"text-xs text-yellow-700 mt-1\">当前温度 {currentMetrics.cpu.temperature}°C</p>\n            </div>\n            \n            <div className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n              <div className=\"flex items-center\">\n                <Activity className=\"h-4 w-4 text-blue-600 mr-2\" />\n                <span className=\"text-sm font-medium text-blue-800\">系统运行正常</span>\n              </div>\n              <p className=\"text-xs text-blue-700 mt-1\">所有服务运行正常</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAhBA;;;;;;;;AAkBA,SAAS;AACT,MAAM,yBAAyB,CAAC,SAAiB,EAAE;IACjD,MAAM,OAAO,EAAE;IACf,MAAM,MAAM,IAAI;IAEhB,IAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,IAAK;QACpC,MAAM,OAAO,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU;;QAC3D,KAAK,IAAI,CAAC;YACR,MAAM,KAAK,kBAAkB;YAC7B,KAAK,KAAK,MAAM,KAAK;YACrB,QAAQ,KAAK,MAAM,KAAK;YACxB,SAAS,KAAK,MAAM,KAAK;YACzB,MAAM,KAAK,MAAM,KAAK;QACxB;IACF;IAEA,OAAO;AACT;AAEe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,KAAK;YAAE,OAAO;YAAI,OAAO;YAAG,OAAO;YAAwB,aAAa;QAAG;QAC3E,QAAQ;YAAE,OAAO,KAAK,OAAO,OAAO;YAAM,MAAM,KAAK,OAAO,OAAO;YAAM,WAAW,KAAK,OAAO,OAAO;QAAK;QAC5G,MAAM;YAAE,OAAO,OAAO,OAAO,OAAO;YAAM,MAAM,MAAM,OAAO,OAAO;YAAM,WAAW,MAAM,OAAO,OAAO;QAAK;QAC9G,SAAS;YAAE,QAAQ;YAAM,UAAU;QAAK;QACxC,QAAQ,QAAQ,IAAI,OAAO,IAAI,KAAK;IACtC;IAEA,MAAM,UAAU;QACd;YAAE,IAAI;YAAc,MAAM;YAAc,QAAQ;QAAS;QACzD;YAAE,IAAI;YAAc,MAAM;YAAa,QAAQ;QAAS;QACxD;YAAE,IAAI;YAAc,MAAM;YAAc,QAAQ;QAAU;KAC3D;IAED,WAAW;IACX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,kBAAkB,CAAA;gBAChB,MAAM,UAAU;uBAAI,KAAK,KAAK,CAAC;iBAAG;gBAClC,MAAM,MAAM,IAAI;gBAChB,QAAQ,IAAI,CAAC;oBACX,MAAM,IAAI,kBAAkB;oBAC5B,KAAK,KAAK,MAAM,KAAK;oBACrB,QAAQ,KAAK,MAAM,KAAK;oBACxB,SAAS,KAAK,MAAM,KAAK;oBACzB,MAAM,KAAK,MAAM,KAAK;gBACxB;gBACA,OAAO;YACT;YAEA,SAAS;YACT,kBAAkB,CAAA,OAAQ,CAAC;oBACzB,GAAG,IAAI;oBACP,KAAK;wBAAE,GAAG,KAAK,GAAG;wBAAE,OAAO,KAAK,MAAM,KAAK;wBAAK,aAAa,KAAK,KAAK,MAAM,KAAK;oBAAG;oBACrF,SAAS;wBAAE,QAAQ,KAAK,MAAM,KAAK;wBAAM,UAAU,KAAK,MAAM,KAAK;oBAAK;gBAC1E,CAAC;QACH,GAAG,OAAO,WAAW;;QAErB,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,UAAU,QAAS;QAC7C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,OAAO,GAAG,KAAK,EAAE,EAAE,MAAM,GAAG,EAAE,QAAQ,EAAE,CAAC;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAE5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,WAAU;0CAET,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC;wCAAuB,OAAO,OAAO,EAAE;;4CACrC,OAAO,IAAI;4CAAC;4CAAG,OAAO,MAAM,KAAK,WAAW,OAAO;4CAAK;;uCAD9C,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,UAAU;wBACT,OAAM;wBACN,OAAO,eAAe,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC;wBACxC,MAAK;wBACL,QAAQ;wBACR,aAAY;wBACZ,oBAAM,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;wBACrB,OAAO,eAAe,GAAG,CAAC,KAAK,GAAG,KAAK,QAAQ,eAAe,GAAG,CAAC,KAAK,GAAG,KAAK,WAAW;;0CAE1F,8OAAC,+IAAA,CAAA,UAAW;gCAAC,OAAO,eAAe,GAAG,CAAC,KAAK;;;;;;0CAC5C,8OAAC;gCAAI,WAAU;;oCACZ,eAAe,GAAG,CAAC,KAAK;oCAAC;oCAAO,eAAe,GAAG,CAAC,WAAW;oCAAC;;;;;;;;;;;;;kCAIpE,8OAAC,8IAAA,CAAA,UAAU;wBACT,OAAM;wBACN,OAAO,CAAC,AAAC,eAAe,MAAM,CAAC,IAAI,GAAG,eAAe,MAAM,CAAC,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;wBAClF,MAAK;wBACL,QAAQ,CAAC;wBACT,aAAY;wBACZ,oBAAM,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC7B,OAAM;;0CAEN,8OAAC,+IAAA,CAAA,UAAW;gCAAC,OAAO,AAAC,eAAe,MAAM,CAAC,IAAI,GAAG,eAAe,MAAM,CAAC,KAAK,GAAI;;;;;;0CACjF,8OAAC;gCAAI,WAAU;;oCACZ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,eAAe,MAAM,CAAC,IAAI;oCAAE;oCAAI,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,eAAe,MAAM,CAAC,KAAK;;;;;;;;;;;;;kCAIxF,8OAAC,8IAAA,CAAA,UAAU;wBACT,OAAM;wBACN,OAAO,CAAC,AAAC,eAAe,IAAI,CAAC,IAAI,GAAG,eAAe,IAAI,CAAC,KAAK,GAAI,GAAG,EAAE,OAAO,CAAC;wBAC9E,MAAK;wBACL,QAAQ;wBACR,aAAY;wBACZ,oBAAM,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAC3B,OAAM;;0CAEN,8OAAC,+IAAA,CAAA,UAAW;gCAAC,OAAO,AAAC,eAAe,IAAI,CAAC,IAAI,GAAG,eAAe,IAAI,CAAC,KAAK,GAAI;;;;;;0CAC7E,8OAAC;gCAAI,WAAU;;oCACZ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,eAAe,IAAI,CAAC,SAAS;oCAAE;;;;;;;;;;;;;kCAIhD,8OAAC,8IAAA,CAAA,UAAU;wBACT,OAAM;wBACN,OAAO,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,eAAe,OAAO,CAAC,QAAQ,GAAG,eAAe,OAAO,CAAC,MAAM;wBAClF,MAAK;wBACL,QAAQ;wBACR,aAAY;wBACZ,oBAAM,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACtB,OAAM;kCAEN,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;;gDAAe,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,eAAe,OAAO,CAAC,QAAQ;gDAAE;;;;;;;;;;;;;8CAE9E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;4CAAK,WAAU;;gDAAe,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,eAAe,OAAO,CAAC,MAAM;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;;;;;;sDACf,8OAAC,qJAAA,CAAA,QAAK;;;;;sDACN,8OAAC,uJAAA,CAAA,UAAO;;;;;sDACR,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,aAAa;4CACb,MAAK;;;;;;sDAEP,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,aAAa;4CACb,MAAK;;;;;;;;;;;;;;;;;;;;;;;kCAOb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;;;;;;sDACf,8OAAC,qJAAA,CAAA,QAAK;;;;;sDACN,8OAAC,uJAAA,CAAA,UAAO;;;;;sDACR,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,MAAK;4CACL,aAAa;4CACb,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAAuB,eAAe,GAAG,CAAC,KAAK;;;;;;;;;;;;kDAEjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAAuB,aAAa,eAAe,MAAM;;;;;;;;;;;;kDAE3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;0DAAuB,IAAI,OAAO,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAc,KAAK;wCAAM,QAAQ;oCAAK;oCAC9C;wCAAE,MAAM;wCAAY,KAAK;wCAAK,QAAQ;oCAAI;oCAC1C;wCAAE,MAAM;wCAAY,KAAK;wCAAK,QAAQ;oCAAI;oCAC1C;wCAAE,MAAM;wCAAgB,KAAK;wCAAK,QAAQ;oCAAI;iCAC/C,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAuB,QAAQ,IAAI;;;;;;kEAClD,8OAAC;wDAAI,WAAU;kEAAyB,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,MAAM,GAAG,OAAO;;;;;;;;;;;;0DAE9E,8OAAC;gDAAI,WAAU;;oDAAuB,QAAQ,GAAG;oDAAC;;;;;;;;uCAL1C;;;;;;;;;;;;;;;;kCAYhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAsC;;;;;;;;;;;;0DAExD,8OAAC;gDAAE,WAAU;;oDAA+B;oDAAM,eAAe,GAAG,CAAC,WAAW;oDAAC;;;;;;;;;;;;;kDAGnF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;0DAEtD,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxD", "debugId": null}}]}
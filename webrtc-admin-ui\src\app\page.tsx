'use client'

import { useState, useEffect } from 'react'
import { Client } from '@/types'
import ClientFilters from '@/components/clients/ClientFilters'
import ClientTable from '@/components/clients/ClientTable'
import Pagination from '@/components/ui/Pagination'

// 模拟数据
const mockClients: Client[] = [
  {
    id: 'client-001',
    name: 'PC-办公室-001',
    ip: '*************',
    location: '北京市 海淀区',
    status: 'online',
    owner: {
      name: '050613',
      uuid: 'a5d368211bec18f3f43aa94492702345'
    },
    os: 'Windows 11 Pro',
    version: '1.2.3',
    lastSeen: '2024-01-15 14:30:00',
    lastOnline: '2025-06-17',
    uptime: 86400,
    tags: ['办公', '生产环境'],
    group: 'production',
    cpu: 45,
    memory: 68,
    disk: 75,
    network: { upload: 1024, download: 2048 }
  },
  {
    id: 'client-002',
    name: 'PC-开发-002',
    ip: '*************',
    location: '上海市 浦东新区',
    status: 'online',
    owner: {
      name: '051201',
      uuid: 'b7f4c9e2d8a6b3f1c5e9a2d7b4f8c1e6'
    },
    os: 'Ubuntu 22.04',
    version: '1.2.3',
    lastSeen: '2024-01-15 14:25:00',
    lastOnline: '2025-06-17',
    uptime: 172800,
    tags: ['开发', '测试环境'],
    group: 'development',
    cpu: 32,
    memory: 54,
    disk: 45,
    network: { upload: 512, download: 1024 }
  },
  {
    id: 'client-003',
    name: 'PC-会议室-003',
    ip: '*************',
    location: '广州市 天河区',
    status: 'offline',
    owner: {
      name: '052408',
      uuid: 'c9e3f7a1d5b8c2f6e0a4d8b2f6c0e4a8'
    },
    os: 'Windows 10 Pro',
    version: '1.2.2',
    lastSeen: '2024-01-15 12:00:00',
    lastOnline: '2025-06-16',
    uptime: 0,
    tags: ['会议室'],
    group: 'production',
    cpu: 0,
    memory: 0,
    disk: 60,
    network: { upload: 0, download: 0 }
  },
  {
    id: 'client-004',
    name: 'PC-财务-004',
    ip: '*************',
    location: '深圳市 南山区',
    status: 'online',
    owner: {
      name: '060815',
      uuid: 'd2f8b4e6c0a3f7d1b5e9c3f7a1d5b9c3'
    },
    os: 'Windows 11 Pro',
    version: '1.2.4',
    lastSeen: '2024-01-15 15:00:00',
    lastOnline: '2025-06-17',
    uptime: 129600,
    tags: ['财务', '生产环境'],
    group: 'production',
    cpu: 38,
    memory: 72,
    disk: 82,
    network: { upload: 768, download: 1536 }
  },
  {
    id: 'client-005',
    name: 'PC-人事-005',
    ip: '*************',
    location: '杭州市 西湖区',
    status: 'connecting',
    owner: {
      name: '071022',
      uuid: 'e4a8d2f6b0c4e8a2d6f0b4c8e2a6d0f4'
    },
    os: 'Windows 10 Pro',
    version: '1.2.1',
    lastSeen: '2024-01-15 13:45:00',
    lastOnline: '2025-06-17',
    uptime: 43200,
    tags: ['人事', '办公'],
    group: 'production',
    cpu: 25,
    memory: 45,
    disk: 65,
    network: { upload: 256, download: 512 }
  },
  {
    id: 'client-006',
    name: 'PC-测试-006',
    ip: '*************',
    location: '成都市 高新区',
    status: 'online',
    owner: {
      name: '081229',
      uuid: 'f6c0e4a8d2f6b0c4e8a2d6f0b4c8e2a6'
    },
    os: 'Ubuntu 20.04',
    version: '1.1.9',
    lastSeen: '2024-01-15 16:20:00',
    lastOnline: '2025-06-17',
    uptime: 259200,
    tags: ['测试', '开发环境'],
    group: 'development',
    cpu: 55,
    memory: 78,
    disk: 45,
    network: { upload: 1024, download: 2048 }
  },
  {
    id: 'client-007',
    name: 'PC-销售-007',
    ip: '*************',
    location: '武汉市 江汉区',
    status: 'offline',
    owner: {
      name: '090406',
      uuid: 'a8d2f6b0c4e8a2d6f0b4c8e2a6d0f4c8'
    },
    os: 'Windows 11 Pro',
    version: '1.2.3',
    lastSeen: '2024-01-15 11:30:00',
    lastOnline: '2025-06-16',
    uptime: 0,
    tags: ['销售', '客户管理'],
    group: 'production',
    cpu: 0,
    memory: 0,
    disk: 78,
    network: { upload: 0, download: 0 }
  },
  {
    id: 'client-008',
    name: 'PC-设计-008',
    ip: '*************',
    location: '西安市 雁塔区',
    status: 'online',
    owner: {
      name: '100513',
      uuid: 'b0c4e8a2d6f0b4c8e2a6d0f4c8e2a6d0'
    },
    os: 'macOS Ventura',
    version: '1.2.2',
    lastSeen: '2024-01-15 14:15:00',
    lastOnline: '2025-06-17',
    uptime: 345600,
    tags: ['设计', '创意'],
    group: 'production',
    cpu: 62,
    memory: 85,
    disk: 92,
    network: { upload: 2048, download: 4096 }
  },
  {
    id: 'client-009',
    name: 'PC-运维-009',
    ip: '*************',
    location: '南京市 建邺区',
    status: 'online',
    owner: {
      name: '111720',
      uuid: 'c4e8a2d6f0b4c8e2a6d0f4c8e2a6d0f4'
    },
    os: 'CentOS 8',
    version: '1.2.5',
    lastSeen: '2024-01-15 15:30:00',
    lastOnline: '2025-06-17',
    uptime: 604800,
    tags: ['运维', '服务器'],
    group: 'production',
    cpu: 28,
    memory: 42,
    disk: 35,
    network: { upload: 512, download: 1024 }
  },
  {
    id: 'client-010',
    name: 'PC-客服-010',
    ip: '*************',
    location: '青岛市 市南区',
    status: 'connecting',
    owner: {
      name: '120927',
      uuid: 'e8a2d6f0b4c8e2a6d0f4c8e2a6d0f4c8'
    },
    os: 'Windows 10 Pro',
    version: '1.2.0',
    lastSeen: '2024-01-15 12:45:00',
    lastOnline: '2025-06-17',
    uptime: 21600,
    tags: ['客服', '支持'],
    group: 'production',
    cpu: 18,
    memory: 35,
    disk: 58,
    network: { upload: 128, download: 256 }
  },
  {
    id: 'client-011',
    name: 'PC-市场-011',
    ip: '*************',
    location: '天津市 和平区',
    status: 'online',
    owner: {
      name: '130104',
      uuid: 'f0b4c8e2a6d0f4c8e2a6d0f4c8e2a6d0'
    },
    os: 'Windows 11 Pro',
    version: '1.2.3',
    lastSeen: '2024-01-15 14:50:00',
    lastOnline: '2025-06-17',
    uptime: 518400,
    tags: ['市场', '推广'],
    group: 'production',
    cpu: 42,
    memory: 58,
    disk: 71,
    network: { upload: 512, download: 1024 }
  },
  {
    id: 'client-012',
    name: 'PC-法务-012',
    ip: '*************',
    location: '重庆市 渝中区',
    status: 'offline',
    owner: {
      name: '140311',
      uuid: 'a2d6f0b4c8e2a6d0f4c8e2a6d0f4c8e2'
    },
    os: 'Windows 10 Pro',
    version: '1.1.8',
    lastSeen: '2024-01-15 10:20:00',
    lastOnline: '2025-06-15',
    uptime: 0,
    tags: ['法务', '合规'],
    group: 'production',
    cpu: 0,
    memory: 0,
    disk: 55,
    network: { upload: 0, download: 0 }
  },
  {
    id: 'client-013',
    name: 'PC-产品-013',
    ip: '*************',
    location: '苏州市 工业园区',
    status: 'online',
    owner: {
      name: '150518',
      uuid: 'b4c8e2a6d0f4c8e2a6d0f4c8e2a6d0f4'
    },
    os: 'macOS Monterey',
    version: '1.2.1',
    lastSeen: '2024-01-15 16:10:00',
    lastOnline: '2025-06-17',
    uptime: 432000,
    tags: ['产品', '规划'],
    group: 'production',
    cpu: 48,
    memory: 76,
    disk: 88,
    network: { upload: 1024, download: 2048 }
  },
  {
    id: 'client-014',
    name: 'PC-质量-014',
    ip: '*************',
    location: '长沙市 岳麓区',
    status: 'connecting',
    owner: {
      name: '160725',
      uuid: 'c8e2a6d0f4c8e2a6d0f4c8e2a6d0f4c8'
    },
    os: 'Ubuntu 22.04',
    version: '1.2.4',
    lastSeen: '2024-01-15 13:25:00',
    lastOnline: '2025-06-17',
    uptime: 86400,
    tags: ['质量', '测试'],
    group: 'development',
    cpu: 35,
    memory: 52,
    disk: 42,
    network: { upload: 256, download: 512 }
  },
  {
    id: 'client-015',
    name: 'PC-采购-015',
    ip: '*************',
    location: '郑州市 金水区',
    status: 'online',
    owner: {
      name: '170901',
      uuid: 'e2a6d0f4c8e2a6d0f4c8e2a6d0f4c8e2'
    },
    os: 'Windows 11 Pro',
    version: '1.2.2',
    lastSeen: '2024-01-15 15:40:00',
    lastOnline: '2025-06-17',
    uptime: 302400,
    tags: ['采购', '供应链'],
    group: 'production',
    cpu: 31,
    memory: 48,
    disk: 67,
    network: { upload: 384, download: 768 }
  },
  {
    id: 'client-016',
    name: 'PC-物流-016',
    ip: '*************',
    location: '济南市 历下区',
    status: 'offline',
    owner: {
      name: '181108',
      uuid: 'f4c8e2a6d0f4c8e2a6d0f4c8e2a6d0f4'
    },
    os: 'Windows 10 Pro',
    version: '1.1.9',
    lastSeen: '2024-01-15 09:15:00',
    lastOnline: '2025-06-15',
    uptime: 0,
    tags: ['物流', '仓储'],
    group: 'production',
    cpu: 0,
    memory: 0,
    disk: 73,
    network: { upload: 0, download: 0 }
  },
  {
    id: 'client-017',
    name: 'PC-安全-017',
    ip: '*************',
    location: '福州市 鼓楼区',
    status: 'online',
    owner: {
      name: '191215',
      uuid: 'a6d0f4c8e2a6d0f4c8e2a6d0f4c8e2a6'
    },
    os: 'CentOS 7',
    version: '1.2.5',
    lastSeen: '2024-01-15 16:00:00',
    lastOnline: '2025-06-17',
    uptime: 1209600,
    tags: ['安全', '监控'],
    group: 'production',
    cpu: 22,
    memory: 38,
    disk: 28,
    network: { upload: 256, download: 512 }
  },
  {
    id: 'client-018',
    name: 'PC-培训-018',
    ip: '*************',
    location: '石家庄市 长安区',
    status: 'connecting',
    owner: {
      name: '200422',
      uuid: 'd0f4c8e2a6d0f4c8e2a6d0f4c8e2a6d0'
    },
    os: 'Windows 11 Pro',
    version: '1.2.0',
    lastSeen: '2024-01-15 12:30:00',
    lastOnline: '2025-06-17',
    uptime: 64800,
    tags: ['培训', '教育'],
    group: 'production',
    cpu: 15,
    memory: 28,
    disk: 45,
    network: { upload: 128, download: 256 }
  },
  {
    id: 'client-019',
    name: 'PC-档案-019',
    ip: '*************',
    location: '合肥市 蜀山区',
    status: 'online',
    owner: {
      name: '210629',
      uuid: 'c2a6d0f4c8e2a6d0f4c8e2a6d0f4c8e2'
    },
    os: 'Windows 10 Pro',
    version: '1.1.7',
    lastSeen: '2024-01-15 14:05:00',
    lastOnline: '2025-06-17',
    uptime: 777600,
    tags: ['档案', '文档'],
    group: 'production',
    cpu: 12,
    memory: 25,
    disk: 89,
    network: { upload: 64, download: 128 }
  },
  {
    id: 'client-020',
    name: 'PC-备份-020',
    ip: '*************',
    location: '南昌市 东湖区',
    status: 'offline',
    owner: {
      name: '220806',
      uuid: 'e4a8d2f6b0c4e8a2d6f0b4c8e2a6d0f4'
    },
    os: 'Ubuntu 18.04',
    version: '1.0.9',
    lastSeen: '2024-01-15 08:45:00',
    lastOnline: '2025-06-14',
    uptime: 0,
    tags: ['备份', '存储'],
    group: 'production',
    cpu: 0,
    memory: 0,
    disk: 95,
    network: { upload: 0, download: 0 }
  }
]

export default function Home() {
  const [clients] = useState<Client[]>(mockClients)
  const [selectedClients, setSelectedClients] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'online' | 'offline' | 'connecting'>('all')
  const [groupFilter, setGroupFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)

  // 筛选客户端
  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         client.ip.includes(searchQuery)
    const matchesStatus = statusFilter === 'all' || client.status === statusFilter
    const matchesGroup = groupFilter === 'all' || client.group === groupFilter

    return matchesSearch && matchesStatus && matchesGroup
  })

  // 分页计算
  const totalPages = Math.ceil(filteredClients.length / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedClients = filteredClients.slice(startIndex, endIndex)

  // 当筛选条件改变时重置到第一页
  useEffect(() => {
    setCurrentPage(1)
  }, [searchQuery, statusFilter, groupFilter])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handlePageSizeChange = (size: number) => {
    setPageSize(size)
    setCurrentPage(1)
  }

  const handleSelectClient = (clientId: string) => {
    setSelectedClients(prev =>
      prev.includes(clientId)
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    )
  }

  const handleSelectAll = (selected: boolean) => {
    setSelectedClients(selected ? paginatedClients.map(c => c.id) : [])
  }

  const handleRemoteConnect = (clientId: string) => {
    console.log('连接到客户端:', clientId)
    // TODO: 实现远程连接逻辑
  }

  const handleFileManage = (clientId: string) => {
    console.log('文件管理:', clientId)
    // TODO: 实现文件管理逻辑
  }

  const handleCmdExecute = (clientId: string) => {
    console.log('CMD命令:', clientId)
    // TODO: 实现CMD命令逻辑
  }

  const handleRefresh = () => {
    console.log('刷新客户端列表')
    // TODO: 实现刷新逻辑
  }

  const handleExport = () => {
    console.log('导出客户端列表')
    // TODO: 实现导出逻辑
  }

  const handleBatchConnect = () => {
    console.log('批量连接客户端:', selectedClients)
    // TODO: 实现批量连接逻辑
  }

  const handleAddClient = () => {
    console.log('添加新客户端')
    // TODO: 实现添加客户端逻辑
  }

  return (
    <div className="space-y-4 animate-in fade-in duration-500">
      <ClientFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        groupFilter={groupFilter}
        onGroupFilterChange={setGroupFilter}
        selectedCount={selectedClients.length}
        onRefresh={handleRefresh}
        onExport={handleExport}
        onBatchConnect={handleBatchConnect}
        onAddClient={handleAddClient}
      />

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <ClientTable
          clients={paginatedClients}
          selectedClients={selectedClients}
          onSelectClient={handleSelectClient}
          onSelectAll={handleSelectAll}
          onRemoteConnect={handleRemoteConnect}
          onFileManage={handleFileManage}
          onCmdExecute={handleCmdExecute}
        />

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          pageSize={pageSize}
          totalItems={filteredClients.length}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
    </div>
  )
}

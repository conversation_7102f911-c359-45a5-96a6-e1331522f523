'use client'

import { useState } from 'react'
import { Client } from '@/types'
import ClientFilters from '@/components/clients/ClientFilters'
import ClientTable from '@/components/clients/ClientTable'

// 模拟数据
const mockClients: Client[] = [
  {
    id: 'client-001',
    name: 'PC-办公室-001',
    ip: '*************',
    location: '北京市 海淀区',
    status: 'online',
    owner: {
      name: '050613',
      uuid: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
    },
    os: 'Windows 11 Pro',
    version: '1.2.3',
    lastSeen: '2024-01-15 14:30:00',
    lastOnline: '2025-06-17',
    uptime: 86400,
    tags: ['办公', '生产环境'],
    group: 'production',
    cpu: 45,
    memory: 68,
    disk: 75,
    network: { upload: 1024, download: 2048 }
  },
  {
    id: 'client-002',
    name: 'PC-开发-002',
    ip: '*************',
    location: '上海市 浦东新区',
    status: 'online',
    owner: {
      name: '051201',
      uuid: 'b2c3d4e5-f6g7-8901-bcde-f23456789012'
    },
    os: 'Ubuntu 22.04',
    version: '1.2.3',
    lastSeen: '2024-01-15 14:25:00',
    lastOnline: '2025-06-17',
    uptime: 172800,
    tags: ['开发', '测试环境'],
    group: 'development',
    cpu: 32,
    memory: 54,
    disk: 45,
    network: { upload: 512, download: 1024 }
  },
  {
    id: 'client-003',
    name: 'PC-会议室-003',
    ip: '*************',
    location: '广州市 天河区',
    status: 'offline',
    owner: {
      name: '052408',
      uuid: 'c3d4e5f6-g7h8-9012-cdef-************'
    },
    os: 'Windows 10 Pro',
    version: '1.2.2',
    lastSeen: '2024-01-15 12:00:00',
    lastOnline: '2025-06-16',
    uptime: 0,
    tags: ['会议室'],
    group: 'production',
    cpu: 0,
    memory: 0,
    disk: 60,
    network: { upload: 0, download: 0 }
  }
]

export default function Home() {
  const [clients] = useState<Client[]>(mockClients)
  const [selectedClients, setSelectedClients] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'online' | 'offline' | 'connecting'>('all')
  const [groupFilter, setGroupFilter] = useState('all')

  // 筛选客户端
  const filteredClients = clients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         client.ip.includes(searchQuery)
    const matchesStatus = statusFilter === 'all' || client.status === statusFilter
    const matchesGroup = groupFilter === 'all' || client.group === groupFilter

    return matchesSearch && matchesStatus && matchesGroup
  })

  const handleSelectClient = (clientId: string) => {
    setSelectedClients(prev =>
      prev.includes(clientId)
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    )
  }

  const handleSelectAll = (selected: boolean) => {
    setSelectedClients(selected ? filteredClients.map(c => c.id) : [])
  }

  const handleRemoteConnect = (clientId: string) => {
    console.log('连接到客户端:', clientId)
    // TODO: 实现远程连接逻辑
  }

  const handleFileManage = (clientId: string) => {
    console.log('文件管理:', clientId)
    // TODO: 实现文件管理逻辑
  }

  const handleCmdExecute = (clientId: string) => {
    console.log('CMD命令:', clientId)
    // TODO: 实现CMD命令逻辑
  }

  const handleRefresh = () => {
    console.log('刷新客户端列表')
    // TODO: 实现刷新逻辑
  }

  const handleExport = () => {
    console.log('导出客户端列表')
    // TODO: 实现导出逻辑
  }

  const handleBatchConnect = () => {
    console.log('批量连接客户端:', selectedClients)
    // TODO: 实现批量连接逻辑
  }

  const handleAddClient = () => {
    console.log('添加新客户端')
    // TODO: 实现添加客户端逻辑
  }

  return (
    <div className="space-y-6 animate-in fade-in duration-500">
      <ClientFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        groupFilter={groupFilter}
        onGroupFilterChange={setGroupFilter}
        selectedCount={selectedClients.length}
        onRefresh={handleRefresh}
        onExport={handleExport}
        onBatchConnect={handleBatchConnect}
        onAddClient={handleAddClient}
      />

      <ClientTable
        clients={filteredClients}
        selectedClients={selectedClients}
        onSelectClient={handleSelectClient}
        onSelectAll={handleSelectAll}
        onRemoteConnect={handleRemoteConnect}
        onFileManage={handleFileManage}
        onCmdExecute={handleCmdExecute}
      />
    </div>
  )
}

'use client'

import { useState, useMemo } from 'react'
import { Monitor, ChevronDown, Wifi, Search, X } from 'lucide-react'
import Badge from '@/components/ui/Badge'

interface Client {
  id: string
  name: string
  ip: string
  status: 'online' | 'offline'
  os: string
  location: string
  uuid: string
  owner: {
    name: string
    uuid: string
  }
  group: string
}

interface ClientSelectorProps {
  selectedClient: Client | null
  onClientSelect: (client: Client) => void
}

const mockClients: Client[] = [
  {
    id: 'client-001',
    name: 'PC-办公室-001',
    ip: '*************',
    status: 'online',
    os: 'Windows 11 Pro',
    location: '北京市 海淀区',
    uuid: '191215',
    owner: {
      name: '张三',
      uuid: 'a50f8f4a8a6d4c6a2a1f0b6e'
    },
    group: '办公'
  },
  {
    id: 'client-002',
    name: 'PC-会议室-020',
    ip: '*************',
    status: 'online',
    os: 'Ubuntu 18.04',
    location: '深圳市 南山区',
    uuid: '220806',
    owner: {
      name: '李四',
      uuid: 'a6d56211bec18f3f43aa'
    },
    group: '会议'
  },
  {
    id: 'client-003',
    name: 'PC-财务-004',
    ip: '*************',
    status: 'online',
    os: 'Windows 11 Pro',
    location: '深圳市 南山区',
    uuid: '060815',
    owner: {
      name: '王五',
      uuid: 'e4a827f60c4e4a2d6f0b'
    },
    group: '财务'
  },
  {
    id: 'client-004',
    name: 'PC-测试-015',
    ip: '*************',
    status: 'online',
    os: 'Windows 11 Pro',
    location: '深圳市 南山区',
    uuid: '170901',
    owner: {
      name: '赵六',
      uuid: 'e2a60f4c8e2a6d0f4c6e'
    },
    group: '开发'
  },
  {
    id: 'client-005',
    name: 'PC-开发-008',
    ip: '*************',
    status: 'online',
    os: 'macOS Sonoma',
    location: '上海市 浦东新区',
    uuid: '081229',
    owner: {
      name: '孙七',
      uuid: 'f6c4e8a2d6f0b4c8e2a6'
    },
    group: '开发'
  },
  {
    id: 'client-006',
    name: 'PC-设计-012',
    ip: '*************',
    status: 'online',
    os: 'Windows 11 Pro',
    location: '广州市 天河区',
    uuid: '050613',
    owner: {
      name: '周八',
      uuid: 'a6d56211bec18f3f43bb'
    },
    group: '设计'
  },
  {
    id: 'client-007',
    name: 'PC-销售-003',
    ip: '*************',
    status: 'online',
    os: 'Windows 10 Pro',
    location: '北京市 朝阳区',
    uuid: '050815',
    owner: {
      name: '吴九',
      uuid: 'c4e8a2d6f0b4c8e2a6d0'
    },
    group: '销售'
  },
  {
    id: 'client-008',
    name: 'PC-运维-009',
    ip: '*************',
    status: 'online',
    os: 'CentOS 8',
    location: '深圳市 福田区',
    uuid: '081229',
    owner: {
      name: '郑十',
      uuid: 'f6c4e8a2d6f0b4c8e2a7'
    },
    group: '运维'
  }
]

export default function ClientSelector({ selectedClient, onClientSelect }: ClientSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedGroups, setSelectedGroups] = useState<string[]>([])

  // 只显示在线客户端
  const onlineClients = mockClients.filter(client => client.status === 'online')

  // 获取所有分组
  const allGroups = useMemo(() => {
    const groups = new Set<string>()
    onlineClients.forEach(client => {
      groups.add(client.group)
    })
    return Array.from(groups).sort()
  }, [onlineClients])

  // 过滤客户端
  const filteredClients = useMemo(() => {
    return onlineClients.filter(client => {
      // 搜索过滤 - 支持搜索客户端名称、归属用户、UUID
      const matchesSearch = searchTerm === '' ||
        client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.owner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.uuid.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.owner.uuid.toLowerCase().includes(searchTerm.toLowerCase())

      // 分组过滤
      const matchesGroup = selectedGroups.length === 0 ||
        selectedGroups.includes(client.group)

      return matchesSearch && matchesGroup
    })
  }, [onlineClients, searchTerm, selectedGroups])

  const handleClientSelect = (client: Client) => {
    onClientSelect(client)
    setIsOpen(false)
    setSearchTerm('')
    setSelectedGroups([])
  }

  const handleGroupToggle = (group: string) => {
    setSelectedGroups(prev =>
      prev.includes(group)
        ? prev.filter(g => g !== group)
        : [...prev, group]
    )
  }

  const clearSearch = () => {
    setSearchTerm('')
  }

  const clearFilters = () => {
    setSearchTerm('')
    setSelectedGroups([])
  }

  return (
    <div className="relative">
      {/* 选择器按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full min-w-64 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      >
        <div className="flex items-center">
          <Monitor className="h-5 w-5 text-gray-400 mr-3" />
          <div className="text-left">
            {selectedClient ? (
              <>
                <div className="text-sm font-medium text-gray-900">
                  {selectedClient.name} • {selectedClient.owner.name}
                </div>
                <div className="text-xs text-gray-500">
                  {selectedClient.ip} • {selectedClient.uuid}
                </div>
              </>
            ) : (
              <div className="text-sm text-gray-500">选择客户端</div>
            )}
          </div>
        </div>
        <div className="flex items-center">
          {selectedClient && (
            <Badge 
              variant={selectedClient.status === 'online' ? 'success' : 'secondary'}
              size="sm"
              className="mr-2"
            >
              {selectedClient.status === 'online' ? '在线' : '离线'}
            </Badge>
          )}
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </button>

      {/* 下拉列表 */}
      {isOpen && (
        <>
          {/* 遮罩层 */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* 下拉菜单 */}
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-[500px] flex flex-col">
            {/* 搜索框 */}
            <div className="p-3 border-b border-gray-200">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索客户端名称、归属用户、UUID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-8 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {searchTerm && (
                  <button
                    onClick={clearSearch}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded"
                  >
                    <X className="h-3 w-3 text-gray-400" />
                  </button>
                )}
              </div>
            </div>

            {/* 分组筛选 */}
            <div className="p-3 border-b border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium text-gray-700">分组筛选</span>
                {selectedGroups.length > 0 && (
                  <button
                    onClick={clearFilters}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    清除筛选
                  </button>
                )}
              </div>
              <div className="flex flex-wrap gap-1">
                {allGroups.map(group => (
                  <button
                    key={group}
                    onClick={() => handleGroupToggle(group)}
                    className={`px-2 py-1 text-xs rounded-full border transition-colors ${
                      selectedGroups.includes(group)
                        ? 'bg-blue-100 border-blue-300 text-blue-700'
                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {group}
                  </button>
                ))}
              </div>
            </div>

            {/* 客户端列表 */}
            <div className="flex-1 overflow-y-auto max-h-80">
              <div className="p-2">
                <div className="flex items-center justify-between px-2 py-1 mb-2">
                  <span className="text-xs font-medium text-gray-500">
                    在线客户端 ({filteredClients.length})
                  </span>
                  {(searchTerm || selectedGroups.length > 0) && (
                    <span className="text-xs text-gray-400">
                      共 {onlineClients.length} 台
                    </span>
                  )}
                </div>

                {filteredClients.length > 0 ? (
                  filteredClients.map((client) => (
                    <button
                      key={client.id}
                      onClick={() => handleClientSelect(client)}
                      className={`w-full flex items-center px-3 py-2 rounded-md text-left hover:bg-gray-50 transition-colors ${
                        selectedClient?.id === client.id ? 'bg-blue-50 border border-blue-200' : ''
                      }`}
                    >
                      <div className="flex items-center flex-1">
                        <Wifi className="h-4 w-4 text-green-500 mr-3 flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900 truncate">
                            {client.name} • {client.owner.name}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {client.ip} • {client.uuid}
                          </div>
                        </div>
                        <Badge variant="success" size="xs" className="ml-2 flex-shrink-0">
                          在线
                        </Badge>
                      </div>
                    </button>
                  ))
                ) : (
                  <div className="px-3 py-8 text-center text-gray-500">
                    <Monitor className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">未找到匹配的客户端</p>
                    <p className="text-xs mt-1">请尝试调整搜索条件或分组筛选</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

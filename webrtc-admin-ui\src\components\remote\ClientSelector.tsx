'use client'

import { useState } from 'react'
import { Monitor, ChevronDown, Wifi, WifiOff } from 'lucide-react'
import Badge from '@/components/ui/Badge'

interface Client {
  id: string
  name: string
  ip: string
  status: 'online' | 'offline'
  os: string
  location: string
}

interface ClientSelectorProps {
  selectedClient: Client | null
  onClientSelect: (client: Client) => void
}

const mockClients: Client[] = [
  {
    id: 'client-001',
    name: 'PC-办公室-001',
    ip: '*************',
    status: 'online',
    os: 'Windows 11 Pro',
    location: '北京市 海淀区'
  },
  {
    id: 'client-002',
    name: 'PC-会议室-020',
    ip: '*************',
    status: 'online',
    os: 'Ubuntu 18.04',
    location: '深圳市 南山区'
  },
  {
    id: 'client-003',
    name: 'PC-财务-004',
    ip: '*************',
    status: 'offline',
    os: 'Windows 11 Pro',
    location: '深圳市 南山区'
  },
  {
    id: 'client-004',
    name: 'PC-测试-015',
    ip: '*************',
    status: 'online',
    os: 'Windows 11 Pro',
    location: '深圳市 南山区'
  }
]

export default function ClientSelector({ selectedClient, onClientSelect }: ClientSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handleClientSelect = (client: Client) => {
    onClientSelect(client)
    setIsOpen(false)
  }

  return (
    <div className="relative">
      {/* 选择器按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full min-w-64 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      >
        <div className="flex items-center">
          <Monitor className="h-5 w-5 text-gray-400 mr-3" />
          <div className="text-left">
            {selectedClient ? (
              <>
                <div className="text-sm font-medium text-gray-900">
                  {selectedClient.name}
                </div>
                <div className="text-xs text-gray-500">
                  {selectedClient.ip} • {selectedClient.os}
                </div>
              </>
            ) : (
              <div className="text-sm text-gray-500">选择客户端</div>
            )}
          </div>
        </div>
        <div className="flex items-center">
          {selectedClient && (
            <Badge 
              variant={selectedClient.status === 'online' ? 'success' : 'secondary'}
              size="sm"
              className="mr-2"
            >
              {selectedClient.status === 'online' ? '在线' : '离线'}
            </Badge>
          )}
          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </button>

      {/* 下拉列表 */}
      {isOpen && (
        <>
          {/* 遮罩层 */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* 下拉菜单 */}
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-80 overflow-y-auto">
            <div className="p-2">
              <div className="text-xs font-medium text-gray-500 px-2 py-1 mb-1">
                在线客户端 ({mockClients.filter(c => c.status === 'online').length})
              </div>
              {mockClients
                .filter(client => client.status === 'online')
                .map((client) => (
                  <button
                    key={client.id}
                    onClick={() => handleClientSelect(client)}
                    className={`w-full flex items-center px-3 py-2 rounded-md text-left hover:bg-gray-50 ${
                      selectedClient?.id === client.id ? 'bg-blue-50 border border-blue-200' : ''
                    }`}
                  >
                    <div className="flex items-center flex-1">
                      <Wifi className="h-4 w-4 text-green-500 mr-3" />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">
                          {client.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {client.ip} • {client.location}
                        </div>
                      </div>
                      <Badge variant="success" size="xs">在线</Badge>
                    </div>
                  </button>
                ))}
              
              {mockClients.filter(c => c.status === 'offline').length > 0 && (
                <>
                  <div className="text-xs font-medium text-gray-500 px-2 py-1 mb-1 mt-3">
                    离线客户端 ({mockClients.filter(c => c.status === 'offline').length})
                  </div>
                  {mockClients
                    .filter(client => client.status === 'offline')
                    .map((client) => (
                      <button
                        key={client.id}
                        onClick={() => handleClientSelect(client)}
                        className="w-full flex items-center px-3 py-2 rounded-md text-left hover:bg-gray-50 opacity-60"
                        disabled
                      >
                        <div className="flex items-center flex-1">
                          <WifiOff className="h-4 w-4 text-gray-400 mr-3" />
                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-600">
                              {client.name}
                            </div>
                            <div className="text-xs text-gray-400">
                              {client.ip} • {client.location}
                            </div>
                          </div>
                          <Badge variant="secondary" size="xs">离线</Badge>
                        </div>
                      </button>
                    ))}
                </>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  )
}

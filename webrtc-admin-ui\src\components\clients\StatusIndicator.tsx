import { cn } from '@/lib/utils'

interface StatusIndicatorProps {
  status: 'online' | 'offline' | 'connecting'
  showText?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export default function StatusIndicator({ 
  status, 
  showText = true, 
  size = 'md' 
}: StatusIndicatorProps) {
  const statusConfig = {
    online: {
      color: 'bg-green-500',
      text: '在线',
      textColor: 'text-green-700'
    },
    offline: {
      color: 'bg-gray-400',
      text: '离线',
      textColor: 'text-gray-700'
    },
    connecting: {
      color: 'bg-yellow-500',
      text: '连接中',
      textColor: 'text-yellow-700'
    }
  }

  const sizeConfig = {
    sm: {
      dot: 'w-2 h-2',
      text: 'text-xs'
    },
    md: {
      dot: 'w-3 h-3',
      text: 'text-sm'
    },
    lg: {
      dot: 'w-4 h-4',
      text: 'text-base'
    }
  }

  const config = statusConfig[status]
  const sizeClass = sizeConfig[size]

  return (
    <div className="flex items-center space-x-2">
      <div
        className={cn(
          'rounded-full',
          config.color,
          sizeClass.dot,
          status === 'connecting' && 'animate-pulse'
        )}
      />
      {showText && (
        <span className={cn(config.textColor, sizeClass.text, 'font-medium')}>
          {config.text}
        </span>
      )}
    </div>
  )
}

'use client'

import { Monitor, FolderOpen, Terminal } from 'lucide-react'

export type TabType = 'remote' | 'files' | 'cmd'

interface TabBarProps {
  activeTab: TabType
  onTabChange: (tab: TabType) => void
  disabled?: boolean
}

export default function TabBar({ activeTab, onTabChange, disabled = false }: TabBarProps) {
  const tabs = [
    {
      id: 'remote' as TabType,
      name: '远程桌面',
      icon: Monitor,
      description: '远程控制客户端桌面'
    },
    {
      id: 'files' as TabType,
      name: '文件管理',
      icon: FolderOpen,
      description: '管理客户端文件系统'
    },
    {
      id: 'cmd' as TabType,
      name: 'CMD命令',
      icon: Terminal,
      description: '执行命令行操作'
    }
  ]

  return (
    <div className="bg-gray-50 border-b border-gray-200">
      <div className="flex">
        {tabs.map((tab) => {
          const Icon = tab.icon
          const isActive = activeTab === tab.id
          
          return (
            <button
              key={tab.id}
              onClick={() => !disabled && onTabChange(tab.id)}
              disabled={disabled}
              className={`
                flex items-center space-x-2 px-6 py-3 border-b-2 transition-all duration-200
                ${isActive 
                  ? 'tab-active' 
                  : 'tab-inactive'
                }
                ${disabled 
                  ? 'opacity-50 cursor-not-allowed' 
                  : 'cursor-pointer'
                }
              `}
              title={tab.description}
            >
              <Icon className="h-4 w-4" />
              <span className="font-medium">{tab.name}</span>
            </button>
          )
        })}
      </div>
    </div>
  )
}

import { cn } from '@/lib/utils'

interface ProgressBarProps {
  value: number
  max?: number
  size?: 'sm' | 'md' | 'lg'
  color?: 'blue' | 'green' | 'yellow' | 'red'
  showLabel?: boolean
  label?: string
}

export default function ProgressBar({
  value,
  max = 100,
  size = 'md',
  color = 'blue',
  showLabel = true,
  label
}: ProgressBarProps) {
  const percentage = Math.min((value / max) * 100, 100)
  
  const sizeClasses = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4'
  }
  
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500'
  }
  
  const getColorByPercentage = (percentage: number) => {
    if (percentage >= 90) return 'red'
    if (percentage >= 75) return 'yellow'
    if (percentage >= 50) return 'blue'
    return 'green'
  }
  
  const autoColor = getColorByPercentage(percentage)
  const finalColor = color === 'blue' ? autoColor : color

  return (
    <div className="w-full">
      {showLabel && (
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm text-gray-600">{label}</span>
          <span className="text-sm font-medium text-gray-900">
            {value}{max === 100 ? '%' : `/${max}`}
          </span>
        </div>
      )}
      <div className={cn('w-full bg-gray-200 rounded-full', sizeClasses[size])}>
        <div
          className={cn(
            'rounded-full transition-all duration-300',
            sizeClasses[size],
            colorClasses[finalColor]
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  )
}

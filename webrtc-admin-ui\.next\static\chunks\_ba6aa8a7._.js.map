{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'\n    }\n    \n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || loading}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport default Button\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/clients/ClientFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Search, Filter, Download, RefreshCw, Plus } from 'lucide-react'\nimport Button from '@/components/ui/Button'\n\ninterface ClientFiltersProps {\n  searchQuery: string\n  onSearchChange: (query: string) => void\n  statusFilter: 'all' | 'online' | 'offline' | 'connecting'\n  onStatusFilterChange: (status: 'all' | 'online' | 'offline' | 'connecting') => void\n  groupFilter: string\n  onGroupFilterChange: (group: string) => void\n  selectedCount: number\n  onRefresh: () => void\n  onExport: () => void\n  onBatchConnect: () => void\n  onAddClient: () => void\n}\n\nconst statusOptions = [\n  { value: 'all', label: '全部状态', count: 15 },\n  { value: 'online', label: '在线', count: 12 },\n  { value: 'offline', label: '离线', count: 2 },\n  { value: 'connecting', label: '连接中', count: 1 }\n]\n\nconst groupOptions = [\n  { value: 'all', label: '全部分组' },\n  { value: 'production', label: '生产环境' },\n  { value: 'development', label: '开发环境' },\n  { value: 'testing', label: '测试环境' }\n]\n\nexport default function ClientFilters({\n  searchQuery,\n  onSearchChange,\n  statusFilter,\n  onStatusFilterChange,\n  groupFilter,\n  onGroupFilterChange,\n  selectedCount,\n  onRefresh,\n  onExport,\n  onBatchConnect,\n  onAddClient\n}: ClientFiltersProps) {\n  const [isFilterOpen, setIsFilterOpen] = useState(false)\n\n  return (\n    <div className=\"bg-white rounded-lg shadow p-4 mb-6 hover-lift\">\n      {/* 顶部操作栏 */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-4\">\n        <div className=\"flex items-center space-x-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">客户端管理</h2>\n          <span className=\"text-sm text-gray-500\">\n            共 {statusOptions.find(s => s.value === 'all')?.count || 0} 台设备\n          </span>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <Button variant=\"outline\" size=\"sm\" onClick={onRefresh}>\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\n            刷新\n          </Button>\n          <Button variant=\"outline\" size=\"sm\" onClick={onExport}>\n            <Download className=\"h-4 w-4 mr-2\" />\n            导出\n          </Button>\n          <Button size=\"sm\" onClick={onAddClient}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            添加客户端\n          </Button>\n        </div>\n      </div>\n\n      {/* 搜索和筛选 */}\n      <div className=\"flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4\">\n        {/* 搜索框 */}\n        <div className=\"flex-1 relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"搜索客户端名称、IP地址...\"\n            value={searchQuery}\n            onChange={(e) => onSearchChange(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 placeholder-gray-500\"\n          />\n        </div>\n\n        {/* 状态筛选 */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-700 whitespace-nowrap\">状态:</span>\n          <select\n            value={statusFilter}\n            onChange={(e) => onStatusFilterChange(e.target.value as any)}\n            className=\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white\"\n          >\n            {statusOptions.map(option => (\n              <option key={option.value} value={option.value}>\n                {option.label} ({option.count})\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* 分组筛选 */}\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-700 whitespace-nowrap\">分组:</span>\n          <select\n            value={groupFilter}\n            onChange={(e) => onGroupFilterChange(e.target.value)}\n            className=\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white\"\n          >\n            {groupOptions.map(option => (\n              <option key={option.value} value={option.value}>\n                {option.label}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* 高级筛选按钮 */}\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setIsFilterOpen(!isFilterOpen)}\n        >\n          <Filter className=\"h-4 w-4 mr-2\" />\n          高级筛选\n        </Button>\n      </div>\n\n      {/* 批量操作栏 */}\n      {selectedCount > 0 && (\n        <div className=\"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200\">\n          <div className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-blue-700\">\n              已选择 {selectedCount} 台设备\n            </span>\n            <div className=\"flex items-center space-x-2\">\n              <Button size=\"sm\" variant=\"outline\" onClick={onBatchConnect}>\n                批量连接\n              </Button>\n              <Button size=\"sm\" variant=\"outline\">\n                批量重启\n              </Button>\n              <Button size=\"sm\" variant=\"outline\">\n                添加标签\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 高级筛选面板 */}\n      {isFilterOpen && (\n        <div className=\"mt-4 p-4 bg-gray-50 rounded-lg border\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                操作系统\n              </label>\n              <select className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\">\n                <option value=\"\">全部</option>\n                <option value=\"windows\">Windows</option>\n                <option value=\"linux\">Linux</option>\n                <option value=\"macos\">macOS</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                最后在线时间\n              </label>\n              <select className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\">\n                <option value=\"\">全部</option>\n                <option value=\"1h\">1小时内</option>\n                <option value=\"24h\">24小时内</option>\n                <option value=\"7d\">7天内</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                标签\n              </label>\n              <input\n                type=\"text\"\n                placeholder=\"输入标签名称\"\n                className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 placeholder-gray-500 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n              />\n            </div>\n          </div>\n          <div className=\"mt-4 flex justify-end space-x-2\">\n            <Button variant=\"outline\" size=\"sm\" onClick={() => setIsFilterOpen(false)}>\n              取消\n            </Button>\n            <Button size=\"sm\">\n              应用筛选\n            </Button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAoBA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAO,OAAO;QAAQ,OAAO;IAAG;IACzC;QAAE,OAAO;QAAU,OAAO;QAAM,OAAO;IAAG;IAC1C;QAAE,OAAO;QAAW,OAAO;QAAM,OAAO;IAAE;IAC1C;QAAE,OAAO;QAAc,OAAO;QAAO,OAAO;IAAE;CAC/C;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAO,OAAO;IAAO;IAC9B;QAAE,OAAO;QAAc,OAAO;IAAO;IACrC;QAAE,OAAO;QAAe,OAAO;IAAO;IACtC;QAAE,OAAO;QAAW,OAAO;IAAO;CACnC;AAEc,SAAS,cAAc,EACpC,WAAW,EACX,cAAc,EACd,YAAY,EACZ,oBAAoB,EACpB,WAAW,EACX,mBAAmB,EACnB,aAAa,EACb,SAAS,EACT,QAAQ,EACR,cAAc,EACd,WAAW,EACQ;;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAK,WAAU;;oCAAwB;oCACnC,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,QAAQ,SAAS;oCAAE;;;;;;;;;;;;;kCAI9D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,UAAM;gCAAC,MAAK;gCAAK,SAAS;;kDACzB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA0C;;;;;;0CAC1D,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCACpD,WAAU;0CAET,cAAc,GAAG,CAAC,CAAA,uBACjB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;;4CAC3C,OAAO,KAAK;4CAAC;4CAAG,OAAO,KAAK;4CAAC;;uCADnB,OAAO,KAAK;;;;;;;;;;;;;;;;kCAQ/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA0C;;;;;;0CAC1D,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,WAAU;0CAET,aAAa,GAAG,CAAC,CAAA,uBAChB,6LAAC;wCAA0B,OAAO,OAAO,KAAK;kDAC3C,OAAO,KAAK;uCADF,OAAO,KAAK;;;;;;;;;;;;;;;;kCAQ/B,6LAAC,qIAAA,CAAA,UAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,gBAAgB,CAAC;;0CAEhC,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMtC,gBAAgB,mBACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;gCAAwB;gCACjC;gCAAc;;;;;;;sCAErB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,SAAS;8CAAgB;;;;;;8CAG7D,6LAAC,qIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAK,SAAQ;8CAAU;;;;;;8CAGpC,6LAAC,qIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAK,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;;;;;;YAS3C,8BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,6LAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,6LAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAG1B,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,6LAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAK;;;;;;;;;;;;;;;;;;0CAGvB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;kCAIhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS,IAAM,gBAAgB;0CAAQ;;;;;;0CAG3E,6LAAC,qIAAA,CAAA,UAAM;gCAAC,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAQ9B;GA1KwB;KAAA", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/clients/StatusIndicator.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface StatusIndicatorProps {\n  status: 'online' | 'offline' | 'connecting'\n  showText?: boolean\n  size?: 'sm' | 'md' | 'lg'\n}\n\nexport default function StatusIndicator({ \n  status, \n  showText = true, \n  size = 'md' \n}: StatusIndicatorProps) {\n  const statusConfig = {\n    online: {\n      color: 'bg-green-500',\n      text: '在线',\n      textColor: 'text-green-700'\n    },\n    offline: {\n      color: 'bg-gray-400',\n      text: '离线',\n      textColor: 'text-gray-700'\n    },\n    connecting: {\n      color: 'bg-yellow-500',\n      text: '连接中',\n      textColor: 'text-yellow-700'\n    }\n  }\n\n  const sizeConfig = {\n    sm: {\n      dot: 'w-2 h-2',\n      text: 'text-xs'\n    },\n    md: {\n      dot: 'w-3 h-3',\n      text: 'text-sm'\n    },\n    lg: {\n      dot: 'w-4 h-4',\n      text: 'text-base'\n    }\n  }\n\n  const config = statusConfig[status]\n  const sizeClass = sizeConfig[size]\n\n  return (\n    <div className=\"flex items-center space-x-2\">\n      <div\n        className={cn(\n          'rounded-full',\n          config.color,\n          sizeClass.dot,\n          status === 'connecting' && 'animate-pulse'\n        )}\n      />\n      {showText && (\n        <span className={cn(config.textColor, sizeClass.text, 'font-medium')}>\n          {config.text}\n        </span>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAQe,SAAS,gBAAgB,EACtC,MAAM,EACN,WAAW,IAAI,EACf,OAAO,IAAI,EACU;IACrB,MAAM,eAAe;QACnB,QAAQ;YACN,OAAO;YACP,MAAM;YACN,WAAW;QACb;QACA,SAAS;YACP,OAAO;YACP,MAAM;YACN,WAAW;QACb;QACA,YAAY;YACV,OAAO;YACP,MAAM;YACN,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,KAAK;YACL,MAAM;QACR;QACA,IAAI;YACF,KAAK;YACL,MAAM;QACR;QACA,IAAI;YACF,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,SAAS,YAAY,CAAC,OAAO;IACnC,MAAM,YAAY,UAAU,CAAC,KAAK;IAElC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,OAAO,KAAK,EACZ,UAAU,GAAG,EACb,WAAW,gBAAgB;;;;;;YAG9B,0BACC,6LAAC;gBAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO,SAAS,EAAE,UAAU,IAAI,EAAE;0BACnD,OAAO,IAAI;;;;;;;;;;;;AAKtB;KA1DwB", "debugId": null}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface BadgeProps extends HTMLAttributes<HTMLSpanElement> {\n  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info'\n  size?: 'sm' | 'md'\n}\n\nexport default function Badge({ \n  className, \n  variant = 'default', \n  size = 'md', \n  children, \n  ...props \n}: BadgeProps) {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full border'\n  \n  const variants = {\n    default: 'bg-gray-100 text-gray-800 border-gray-200',\n    success: 'bg-green-100 text-green-800 border-green-200',\n    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    danger: 'bg-red-100 text-red-800 border-red-200',\n    info: 'bg-blue-100 text-blue-800 border-blue-200'\n  }\n  \n  const sizes = {\n    sm: 'px-2 py-0.5 text-xs',\n    md: 'px-2.5 py-1 text-sm'\n  }\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,MAAM,EAC5B,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACQ;IACX,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KAnCwB", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/clients/ClientTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Monitor, MoreVertical, Play, RotateCcw, Eye, Tag } from 'lucide-react'\nimport { Client } from '@/types'\nimport { formatUptime } from '@/lib/utils'\nimport StatusIndicator from './StatusIndicator'\nimport <PERSON><PERSON> from '@/components/ui/Button'\nimport Badge from '@/components/ui/Badge'\n\ninterface ClientTableProps {\n  clients: Client[]\n  selectedClients: string[]\n  onSelectClient: (clientId: string) => void\n  onSelectAll: (selected: boolean) => void\n  onRemoteConnect: (clientId: string) => void\n  onRestart: (clientId: string) => void\n  onViewDetails: (clientId: string) => void\n}\n\nexport default function ClientTable({\n  clients,\n  selectedClients,\n  onSelectClient,\n  onSelectAll,\n  onRemoteConnect,\n  onRestart,\n  onViewDetails\n}: ClientTableProps) {\n  const [sortField, setSortField] = useState<keyof Client>('name')\n  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')\n\n  const handleSort = (field: keyof Client) => {\n    if (sortField === field) {\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')\n    } else {\n      setSortField(field)\n      setSortDirection('asc')\n    }\n  }\n\n  const sortedClients = [...clients].sort((a, b) => {\n    const aValue = a[sortField]\n    const bValue = b[sortField]\n    \n    if (typeof aValue === 'string' && typeof bValue === 'string') {\n      return sortDirection === 'asc' \n        ? aValue.localeCompare(bValue)\n        : bValue.localeCompare(aValue)\n    }\n    \n    if (typeof aValue === 'number' && typeof bValue === 'number') {\n      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue\n    }\n    \n    return 0\n  })\n\n  const isAllSelected = clients.length > 0 && selectedClients.length === clients.length\n\n  return (\n    <div className=\"bg-white rounded-lg shadow overflow-hidden hover-lift\">\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\"\n               style={{ minWidth: '800px' }}>\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left\">\n                <input\n                  type=\"checkbox\"\n                  checked={isAllSelected}\n                  onChange={(e) => onSelectAll(e.target.checked)}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n              </th>\n              <th \n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                onClick={() => handleSort('name')}\n              >\n                客户端名称\n              </th>\n              <th \n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                onClick={() => handleSort('ip')}\n              >\n                IP地址\n              </th>\n              <th \n                className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100\"\n                onClick={() => handleSort('status')}\n              >\n                状态\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                系统信息\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                运行时间\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                标签\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                操作\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {sortedClients.map((client) => (\n              <tr key={client.id} className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedClients.includes(client.id)}\n                    onChange={() => onSelectClient(client.id)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"flex items-center\">\n                    <Monitor className=\"h-5 w-5 text-gray-400 mr-3\" />\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900\">{client.name}</div>\n                      <div className=\"text-sm text-gray-500\">{client.id}</div>\n                    </div>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {client.ip}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <StatusIndicator status={client.status} />\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  <div>\n                    <div>{client.os}</div>\n                    <div className=\"text-gray-500\">v{client.version}</div>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {formatUptime(client.uptime)}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"flex flex-wrap gap-1\">\n                    {client.tags.map((tag, index) => (\n                      <Badge key={index} variant=\"info\" size=\"sm\">\n                        {tag}\n                      </Badge>\n                    ))}\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Button\n                      size=\"sm\"\n                      variant={client.status === 'online' ? 'primary' : 'outline'}\n                      disabled={client.status !== 'online'}\n                      onClick={() => onRemoteConnect(client.id)}\n                    >\n                      <Play className=\"h-4 w-4 mr-1\" />\n                      远程\n                    </Button>\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => onViewDetails(client.id)}\n                    >\n                      <Eye className=\"h-4 w-4 mr-1\" />\n                      查看\n                    </Button>\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => onRestart(client.id)}\n                    >\n                      <RotateCcw className=\"h-4 w-4 mr-1\" />\n                      重启\n                    </Button>\n                  </div>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;;AARA;;;;;;;AAoBe,SAAS,YAAY,EAClC,OAAO,EACP,eAAe,EACf,cAAc,EACd,WAAW,EACX,eAAe,EACf,SAAS,EACT,aAAa,EACI;;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA,MAAM,gBAAgB;WAAI;KAAQ,CAAC,IAAI,CAAC,CAAC,GAAG;QAC1C,MAAM,SAAS,CAAC,CAAC,UAAU;QAC3B,MAAM,SAAS,CAAC,CAAC,UAAU;QAE3B,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;YAC5D,OAAO,kBAAkB,QACrB,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;QAC3B;QAEA,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;YAC5D,OAAO,kBAAkB,QAAQ,SAAS,SAAS,SAAS;QAC9D;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB,QAAQ,MAAM,GAAG,KAAK,gBAAgB,MAAM,KAAK,QAAQ,MAAM;IAErF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAM,WAAU;gBACV,OAAO;oBAAE,UAAU;gBAAQ;;kCAChC,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,OAAO;wCAC7C,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,WAAW;8CAC3B;;;;;;8CAGD,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,WAAW;8CAC3B;;;;;;8CAGD,6LAAC;oCACC,WAAU;oCACV,SAAS,IAAM,WAAW;8CAC3B;;;;;;8CAGD,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;8CAG/F,6LAAC;oCAAG,WAAU;8CAAiF;;;;;;;;;;;;;;;;;kCAKnG,6LAAC;wBAAM,WAAU;kCACd,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;gCAAmB,WAAU;;kDAC5B,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CACC,MAAK;4CACL,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;4CAC3C,UAAU,IAAM,eAAe,OAAO,EAAE;4CACxC,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAqC,OAAO,IAAI;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;sEAAyB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAIvD,6LAAC;wCAAG,WAAU;kDACX,OAAO,EAAE;;;;;;kDAEZ,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC,mJAAA,CAAA,UAAe;4CAAC,QAAQ,OAAO,MAAM;;;;;;;;;;;kDAExC,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;;8DACC,6LAAC;8DAAK,OAAO,EAAE;;;;;;8DACf,6LAAC;oDAAI,WAAU;;wDAAgB;wDAAE,OAAO,OAAO;;;;;;;;;;;;;;;;;;kDAGnD,6LAAC;wCAAG,WAAU;kDACX,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,MAAM;;;;;;kDAE7B,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;sDACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACrB,6LAAC,oIAAA,CAAA,UAAK;oDAAa,SAAQ;oDAAO,MAAK;8DACpC;mDADS;;;;;;;;;;;;;;;kDAMlB,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,UAAM;oDACL,MAAK;oDACL,SAAS,OAAO,MAAM,KAAK,WAAW,YAAY;oDAClD,UAAU,OAAO,MAAM,KAAK;oDAC5B,SAAS,IAAM,gBAAgB,OAAO,EAAE;;sEAExC,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,6LAAC,qIAAA,CAAA,UAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,cAAc,OAAO,EAAE;;sEAEtC,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGlC,6LAAC,qIAAA,CAAA,UAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,UAAU,OAAO,EAAE;;sEAElC,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;+BAlErC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EhC;GAvKwB;KAAA", "debugId": null}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Client } from '@/types'\nimport ClientFilters from '@/components/clients/ClientFilters'\nimport ClientTable from '@/components/clients/ClientTable'\n\n// 模拟数据\nconst mockClients: Client[] = [\n  {\n    id: 'client-001',\n    name: 'PC-办公室-001',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    version: '1.2.3',\n    lastSeen: '2024-01-15 14:30:00',\n    uptime: 86400,\n    tags: ['办公', '生产环境'],\n    group: 'production',\n    cpu: 45,\n    memory: 68,\n    disk: 75,\n    network: { upload: 1024, download: 2048 }\n  },\n  {\n    id: 'client-002',\n    name: 'PC-开发-002',\n    ip: '*************',\n    status: 'online',\n    os: 'Ubuntu 22.04',\n    version: '1.2.3',\n    lastSeen: '2024-01-15 14:25:00',\n    uptime: 172800,\n    tags: ['开发', '测试环境'],\n    group: 'development',\n    cpu: 32,\n    memory: 54,\n    disk: 45,\n    network: { upload: 512, download: 1024 }\n  },\n  {\n    id: 'client-003',\n    name: 'PC-会议室-003',\n    ip: '*************',\n    status: 'offline',\n    os: 'Windows 10 Pro',\n    version: '1.2.2',\n    lastSeen: '2024-01-15 12:00:00',\n    uptime: 0,\n    tags: ['会议室'],\n    group: 'production',\n    cpu: 0,\n    memory: 0,\n    disk: 60,\n    network: { upload: 0, download: 0 }\n  }\n]\n\nexport default function Home() {\n  const [clients] = useState<Client[]>(mockClients)\n  const [selectedClients, setSelectedClients] = useState<string[]>([])\n  const [searchQuery, setSearchQuery] = useState('')\n  const [statusFilter, setStatusFilter] = useState<'all' | 'online' | 'offline' | 'connecting'>('all')\n  const [groupFilter, setGroupFilter] = useState('all')\n\n  // 筛选客户端\n  const filteredClients = clients.filter(client => {\n    const matchesSearch = client.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         client.ip.includes(searchQuery)\n    const matchesStatus = statusFilter === 'all' || client.status === statusFilter\n    const matchesGroup = groupFilter === 'all' || client.group === groupFilter\n\n    return matchesSearch && matchesStatus && matchesGroup\n  })\n\n  const handleSelectClient = (clientId: string) => {\n    setSelectedClients(prev =>\n      prev.includes(clientId)\n        ? prev.filter(id => id !== clientId)\n        : [...prev, clientId]\n    )\n  }\n\n  const handleSelectAll = (selected: boolean) => {\n    setSelectedClients(selected ? filteredClients.map(c => c.id) : [])\n  }\n\n  const handleRemoteConnect = (clientId: string) => {\n    console.log('连接到客户端:', clientId)\n    // TODO: 实现远程连接逻辑\n  }\n\n  const handleRestart = (clientId: string) => {\n    console.log('重启客户端:', clientId)\n    // TODO: 实现重启逻辑\n  }\n\n  const handleViewDetails = (clientId: string) => {\n    console.log('查看客户端详情:', clientId)\n    // TODO: 实现查看详情逻辑\n  }\n\n  const handleRefresh = () => {\n    console.log('刷新客户端列表')\n    // TODO: 实现刷新逻辑\n  }\n\n  const handleExport = () => {\n    console.log('导出客户端列表')\n    // TODO: 实现导出逻辑\n  }\n\n  const handleBatchConnect = () => {\n    console.log('批量连接客户端:', selectedClients)\n    // TODO: 实现批量连接逻辑\n  }\n\n  const handleAddClient = () => {\n    console.log('添加新客户端')\n    // TODO: 实现添加客户端逻辑\n  }\n\n  return (\n    <div className=\"space-y-6 animate-in fade-in duration-500\">\n      <ClientFilters\n        searchQuery={searchQuery}\n        onSearchChange={setSearchQuery}\n        statusFilter={statusFilter}\n        onStatusFilterChange={setStatusFilter}\n        groupFilter={groupFilter}\n        onGroupFilterChange={setGroupFilter}\n        selectedCount={selectedClients.length}\n        onRefresh={handleRefresh}\n        onExport={handleExport}\n        onBatchConnect={handleBatchConnect}\n        onAddClient={handleAddClient}\n      />\n\n      <ClientTable\n        clients={filteredClients}\n        selectedClients={selectedClients}\n        onSelectClient={handleSelectClient}\n        onSelectAll={handleSelectAll}\n        onRemoteConnect={handleRemoteConnect}\n        onRestart={handleRestart}\n        onViewDetails={handleViewDetails}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;AALA;;;;AAOA,OAAO;AACP,MAAM,cAAwB;IAC5B;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;YAAC;YAAM;SAAO;QACpB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAM,UAAU;QAAK;IAC1C;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;YAAC;YAAM;SAAO;QACpB,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAK,UAAU;QAAK;IACzC;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,SAAS;QACT,UAAU;QACV,QAAQ;QACR,MAAM;YAAC;SAAM;QACb,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,SAAS;YAAE,QAAQ;YAAG,UAAU;QAAE;IACpC;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C;IAC9F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,QAAQ;IACR,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,OAAO,EAAE,CAAC,QAAQ,CAAC;QACxC,MAAM,gBAAgB,iBAAiB,SAAS,OAAO,MAAM,KAAK;QAClE,MAAM,eAAe,gBAAgB,SAAS,OAAO,KAAK,KAAK;QAE/D,OAAO,iBAAiB,iBAAiB;IAC3C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB,CAAA,OACjB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,YACzB;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,kBAAkB,CAAC;QACvB,mBAAmB,WAAW,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,IAAI,EAAE;IACnE;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,WAAW;IACvB,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAC;QACrB,QAAQ,GAAG,CAAC,UAAU;IACtB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,YAAY;IACxB,iBAAiB;IACnB;IAEA,MAAM,gBAAgB;QACpB,QAAQ,GAAG,CAAC;IACZ,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC;IACZ,eAAe;IACjB;IAEA,MAAM,qBAAqB;QACzB,QAAQ,GAAG,CAAC,YAAY;IACxB,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,QAAQ,GAAG,CAAC;IACZ,kBAAkB;IACpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,iJAAA,CAAA,UAAa;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,sBAAsB;gBACtB,aAAa;gBACb,qBAAqB;gBACrB,eAAe,gBAAgB,MAAM;gBACrC,WAAW;gBACX,UAAU;gBACV,gBAAgB;gBAChB,aAAa;;;;;;0BAGf,6LAAC,+IAAA,CAAA,UAAW;gBACV,SAAS;gBACT,iBAAiB;gBACjB,gBAAgB;gBAChB,aAAa;gBACb,iBAAiB;gBACjB,WAAW;gBACX,eAAe;;;;;;;;;;;;AAIvB;GA3FwB;KAAA", "debugId": null}}, {"offset": {"line": 1378, "column": 0}, "map": {"version": 3, "file": "funnel.js", "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/node_modules/lucide-react/src/icons/funnel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z',\n      key: 'sc7q7i',\n    },\n  ],\n];\n\n/**\n * @component @name Funnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBhMSAxIDAgMCAwIC41NTMuODk1bDIgMUExIDEgMCAwIDAgMTQgMjF2LTdhMiAyIDAgMCAxIC41MTctMS4zNDFMMjEuNzQgNC42N0ExIDEgMCAwIDAgMjEgM0gzYTEgMSAwIDAgMC0uNzQyIDEuNjdsNy4yMjUgNy45ODlBMiAyIDAgMCAxIDEwIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/funnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Funnel = createLucideIcon('funnel', __iconNode);\n\nexport default Funnel;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "file": "download.js", "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/node_modules/lucide-react/src/icons/download.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 15V3', key: 'm9g1x1' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['path', { d: 'm7 10 5 5 5-5', key: 'brsn70' }],\n];\n\n/**\n * @component @name Download\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTVWMyIgLz4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cGF0aCBkPSJtNyAxMCA1IDUgNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/download\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Download = createLucideIcon('download', __iconNode);\n\nexport default Download;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1470, "column": 0}, "map": {"version": 3, "file": "refresh-cw.js", "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/node_modules/lucide-react/src/icons/refresh-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('refresh-cw', __iconNode);\n\nexport default RefreshCw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAuD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACpF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1530, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,SAAW,CAAA;QAAA,CAAA;YAAE,QAAQ,oBAAsB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1615, "column": 0}, "map": {"version": 3, "file": "rotate-ccw.js", "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/node_modules/lucide-react/src/icons/rotate-ccw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8', key: '1357e3' }],\n  ['path', { d: 'M3 3v5h5', key: '1xhq8a' }],\n];\n\n/**\n * @component @name RotateCcw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAxIDAgOS05IDkuNzUgOS43NSAwIDAgMC02Ljc0IDIuNzRMMyA4IiAvPgogIDxwYXRoIGQ9Ik0zIDN2NWg1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/rotate-ccw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RotateCcw = createLucideIcon('rotate-ccw', __iconNode);\n\nexport default RotateCcw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1661, "column": 0}, "map": {"version": 3, "file": "eye.js", "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
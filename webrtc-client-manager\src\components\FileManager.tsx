'use client'

import React, { useState, useEffect } from 'react'
import {
  FolderOpen,
  Upload,
  Download,
  Search,
  Grid3X3,
  List,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  RefreshCw,
  ChevronRight,
  Folder,
  File,
  FileText,
  FileImage,
  FileVideo,
  FileAudio,
  Archive,
  Code,
  Edit,
  Copy,
  Trash2,
  MoreHorizontal,
  SortAsc,
  WifiOff
} from 'lucide-react'
import { Client, FileItem } from '@/types/client'

interface FileManagerProps {
  client: Client | null
}

export default function FileManager({ client }: FileManagerProps) {
  const [currentPath, setCurrentPath] = useState('C:\\')
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])
  const [navigationHistory, setNavigationHistory] = useState<string[]>(['C:\\'])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  // 模拟文件数据
  const mockFiles: FileItem[] = [
    { id: '1', name: 'Desktop', type: 'folder', modified: '2024-01-15 10:30', path: 'C:\\Users\\<USER>\\Desktop' },
    { id: '2', name: 'Documents', type: 'folder', modified: '2024-01-14 15:20', path: 'C:\\Users\\<USER>\\Documents' },
    { id: '3', name: 'Downloads', type: 'folder', modified: '2024-01-13 09:45', path: 'C:\\Users\\<USER>\\Downloads' },
    { id: '4', name: 'Pictures', type: 'folder', modified: '2024-01-12 14:15', path: 'C:\\Users\\<USER>\\Pictures' },
    { id: '5', name: 'report.docx', type: 'file', size: 2048576, modified: '2024-01-15 16:30', path: 'C:\\Users\\<USER>\\report.docx' },
    { id: '6', name: 'presentation.pptx', type: 'file', size: 5242880, modified: '2024-01-14 11:20', path: 'C:\\Users\\<USER>\\presentation.pptx' },
    { id: '7', name: 'data.xlsx', type: 'file', size: 1048576, modified: '2024-01-13 13:45', path: 'C:\\Users\\<USER>\\data.xlsx' },
    { id: '8', name: 'image.jpg', type: 'file', size: 3145728, modified: '2024-01-12 08:30', path: 'C:\\Users\\<USER>\\image.jpg' }
  ]

  // 当客户端改变时，重置路径
  useEffect(() => {
    if (client) {
      setCurrentPath('C:\\')
      setNavigationHistory(['C:\\'])
      setHistoryIndex(0)
      setSelectedFiles([])
      setSearchTerm('')
      
      // 模拟加载文件列表
      setIsLoading(true)
      setTimeout(() => {
        setIsLoading(false)
      }, 800)
    }
  }, [client])

  const filteredFiles = mockFiles.filter(file =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const navigateToPath = (path: string) => {
    setCurrentPath(path)
    const newHistory = navigationHistory.slice(0, historyIndex + 1)
    newHistory.push(path)
    setNavigationHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
    setSelectedFiles([])
    
    // 模拟加载
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
    }, 500)
  }

  const goBack = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setCurrentPath(navigationHistory[historyIndex - 1])
      setSelectedFiles([])
    }
  }

  const goForward = () => {
    if (historyIndex < navigationHistory.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setCurrentPath(navigationHistory[historyIndex + 1])
      setSelectedFiles([])
    }
  }

  const goUp = () => {
    const parentPath = currentPath.split('\\').slice(0, -1).join('\\')
    if (parentPath && parentPath !== currentPath) {
      navigateToPath(parentPath || 'C:\\')
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'folder') {
      return <Folder className="h-4 w-4 text-blue-500" />
    }
    
    const extension = file.name.split('.').pop()?.toLowerCase()
    switch (extension) {
      case 'txt':
      case 'doc':
      case 'docx':
        return <FileText className="h-4 w-4 text-blue-600" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FileImage className="h-4 w-4 text-green-600" />
      case 'mp4':
      case 'avi':
      case 'mov':
        return <FileVideo className="h-4 w-4 text-purple-600" />
      case 'mp3':
      case 'wav':
      case 'flac':
        return <FileAudio className="h-4 w-4 text-orange-600" />
      case 'zip':
      case 'rar':
      case '7z':
        return <Archive className="h-4 w-4 text-yellow-600" />
      case 'js':
      case 'ts':
      case 'html':
      case 'css':
        return <Code className="h-4 w-4 text-red-600" />
      default:
        return <File className="h-4 w-4 text-gray-500" />
    }
  }

  // 路径分段处理
  const pathSegments = currentPath.split(/[:/\\]/).filter(Boolean)

  if (!client) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <FolderOpen className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">文件管理</h3>
          <p className="text-gray-500">请从左侧选择一个客户端开始文件管理</p>
        </div>
      </div>
    )
  }

  if (client.status === 'offline') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <WifiOff className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">客户端离线</h3>
          <p className="text-gray-500">客户端 {client.name} 当前离线，无法访问文件系统</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-white">
      {/* 工具栏 */}
      <div className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between">
          {/* 导航按钮 */}
          <div className="flex items-center space-x-1">
            <button
              onClick={goBack}
              disabled={historyIndex <= 0}
              className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowLeft className="h-4 w-4" />
            </button>
            <button
              onClick={goForward}
              disabled={historyIndex >= navigationHistory.length - 1}
              className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowRight className="h-4 w-4" />
            </button>
            <button
              onClick={goUp}
              className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded"
            >
              <ArrowUp className="h-4 w-4" />
            </button>
            <button 
              onClick={() => {
                setIsLoading(true)
                setTimeout(() => setIsLoading(false), 500)
              }}
              className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
          </div>

          {/* 右侧操作按钮 */}
          <div className="flex items-center space-x-2">
            <button className="px-3 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 flex items-center">
              <Upload className="h-4 w-4 mr-1" />
              上传
            </button>
            <button className="px-3 py-1.5 bg-green-600 text-white rounded text-sm hover:bg-green-700 flex items-center">
              <FolderOpen className="h-4 w-4 mr-1" />
              新建文件夹
            </button>
            
            {/* 视图切换 */}
            <div className="flex items-center space-x-1 ml-4">
              <button
                onClick={() => setViewMode('list')}
                className={`p-1.5 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-200'}`}
              >
                <List className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-1.5 rounded ${viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-200'}`}
              >
                <Grid3X3 className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 地址栏 */}
      <div className="bg-gray-50 border-b border-gray-200 px-4 py-2">
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 text-sm">
            {pathSegments.map((segment, index) => (
              <div key={index} className="flex items-center">
                {index > 0 && <ChevronRight className="h-3 w-3 text-gray-400 mx-1" />}
                <button
                  onClick={() => {
                    const newPath = pathSegments.slice(0, index + 1).join('/')
                    navigateToPath(newPath.includes(':') ? newPath : 'C:/' + newPath)
                  }}
                  className="px-2 py-1 text-gray-700 hover:bg-gray-100 rounded"
                >
                  {segment}
                </button>
              </div>
            ))}
          </div>
          <div className="flex-1" />
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索文件..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-1.5 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-64"
            />
          </div>
        </div>
      </div>

      {/* 文件列表内容 */}
      <div className="flex-1 overflow-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-sm text-gray-500">正在加载文件列表...</p>
            </div>
          </div>
        ) : viewMode === 'list' ? (
          /* 列表视图 */
          <table className="w-full">
            <thead className="bg-gray-50 sticky top-0 border-b border-gray-200">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedFiles(filteredFiles.map(f => f.id))
                      } else {
                        setSelectedFiles([])
                      }
                    }}
                  />
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  名称
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                  大小
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
                  修改时间
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {filteredFiles.map((file) => (
                <tr
                  key={file.id}
                  className={`hover:bg-gray-50 cursor-pointer ${
                    selectedFiles.includes(file.id) ? 'bg-blue-50' : ''
                  }`}
                  onDoubleClick={() => {
                    if (file.type === 'folder') {
                      navigateToPath(file.path)
                    }
                  }}
                >
                  <td className="px-4 py-2">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      checked={selectedFiles.includes(file.id)}
                      onChange={(e) => {
                        e.stopPropagation()
                        if (e.target.checked) {
                          setSelectedFiles([...selectedFiles, file.id])
                        } else {
                          setSelectedFiles(selectedFiles.filter(id => id !== file.id))
                        }
                      }}
                    />
                  </td>
                  <td className="px-4 py-2">
                    <div className="flex items-center">
                      {getFileIcon(file)}
                      <span className="ml-2 text-sm text-gray-900 truncate">
                        {file.name}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-500">
                    {file.type === 'file' && file.size ? formatFileSize(file.size) : ''}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-500">
                    {file.modified}
                  </td>
                  <td className="px-4 py-2">
                    <div className="flex items-center space-x-1">
                      <button className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded">
                        <Download className="h-3 w-3" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded">
                        <Edit className="h-3 w-3" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded">
                        <Copy className="h-3 w-3" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded">
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          /* 网格视图 */
          <div className="p-4 grid grid-cols-6 gap-4">
            {filteredFiles.map((file) => (
              <div
                key={file.id}
                className={`p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer text-center ${
                  selectedFiles.includes(file.id) ? 'bg-blue-50 border-blue-300' : ''
                }`}
                onDoubleClick={() => {
                  if (file.type === 'folder') {
                    navigateToPath(file.path)
                  }
                }}
                onClick={() => {
                  if (selectedFiles.includes(file.id)) {
                    setSelectedFiles(selectedFiles.filter(id => id !== file.id))
                  } else {
                    setSelectedFiles([...selectedFiles, file.id])
                  }
                }}
              >
                <div className="flex justify-center mb-2">
                  {React.cloneElement(getFileIcon(file), { className: 'h-8 w-8' })}
                </div>
                <div className="text-xs text-gray-900 truncate" title={file.name}>
                  {file.name}
                </div>
                {file.type === 'file' && file.size && (
                  <div className="text-xs text-gray-500 mt-1">
                    {formatFileSize(file.size)}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* 空状态 */}
        {!isLoading && filteredFiles.length === 0 && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Folder className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-sm font-medium text-gray-900 mb-2">此文件夹为空</h3>
              <p className="text-sm text-gray-500">
                {searchTerm ? '没有找到匹配的文件' : '此文件夹中没有任何文件或文件夹'}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* 底部状态栏 */}
      {selectedFiles.length > 0 && (
        <div className="bg-blue-50 border-t border-blue-200 px-4 py-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-blue-700">已选择 {selectedFiles.length} 个项目</span>
            <div className="flex items-center space-x-2">
              <button className="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700">
                下载
              </button>
              <button className="px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700">
                删除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

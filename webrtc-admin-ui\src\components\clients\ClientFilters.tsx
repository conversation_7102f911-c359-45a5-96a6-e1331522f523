'use client'

import { useState } from 'react'
import { Search, Filter, Download, RefreshCw, Plus } from 'lucide-react'
import Button from '@/components/ui/Button'

interface ClientFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  statusFilter: 'all' | 'online' | 'offline' | 'connecting'
  onStatusFilterChange: (status: 'all' | 'online' | 'offline' | 'connecting') => void
  groupFilter: string
  onGroupFilterChange: (group: string) => void
  selectedCount: number
  onRefresh: () => void
  onExport: () => void
  onBatchConnect: () => void
  onAddClient: () => void
}

const statusOptions = [
  { value: 'all', label: '全部状态', count: 15 },
  { value: 'online', label: '在线', count: 12 },
  { value: 'offline', label: '离线', count: 2 },
  { value: 'connecting', label: '连接中', count: 1 }
]

const groupOptions = [
  { value: 'all', label: '全部分组' },
  { value: 'production', label: '生产环境' },
  { value: 'development', label: '开发环境' },
  { value: 'testing', label: '测试环境' }
]

export default function ClientFilters({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  groupFilter,
  onGroupFilterChange,
  selectedCount,
  onRefresh,
  onExport,
  onBatchConnect,
  onAddClient
}: ClientFiltersProps) {
  const [isFilterOpen, setIsFilterOpen] = useState(false)

  return (
    <div className="bg-white rounded-lg shadow p-4 mb-6 hover-lift">
      {/* 顶部操作栏 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold text-gray-900">客户端管理</h2>
          <span className="text-sm text-gray-500">
            共 {statusOptions.find(s => s.value === 'all')?.count || 0} 台设备
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </Button>
          <Button variant="outline" size="sm" onClick={onExport}>
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
          <Button size="sm" onClick={onAddClient}>
            <Plus className="h-4 w-4 mr-2" />
            添加客户端
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
        {/* 搜索框 */}
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索客户端名称、IP地址..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 placeholder-gray-500"
          />
        </div>

        {/* 状态筛选 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700 whitespace-nowrap">状态:</span>
          <select
            value={statusFilter}
            onChange={(e) => onStatusFilterChange(e.target.value as any)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white"
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label} ({option.count})
              </option>
            ))}
          </select>
        </div>

        {/* 分组筛选 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700 whitespace-nowrap">分组:</span>
          <select
            value={groupFilter}
            onChange={(e) => onGroupFilterChange(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none text-gray-900 bg-white"
          >
            {groupOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* 高级筛选按钮 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsFilterOpen(!isFilterOpen)}
        >
          <Filter className="h-4 w-4 mr-2" />
          高级筛选
        </Button>
      </div>

      {/* 批量操作栏 */}
      {selectedCount > 0 && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              已选择 {selectedCount} 台设备
            </span>
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="outline" onClick={onBatchConnect}>
                批量连接
              </Button>
              <Button size="sm" variant="outline">
                批量重启
              </Button>
              <Button size="sm" variant="outline">
                添加标签
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 高级筛选面板 */}
      {isFilterOpen && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                操作系统
              </label>
              <select className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none">
                <option value="">全部</option>
                <option value="windows">Windows</option>
                <option value="linux">Linux</option>
                <option value="macos">macOS</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最后在线时间
              </label>
              <select className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none">
                <option value="">全部</option>
                <option value="1h">1小时内</option>
                <option value="24h">24小时内</option>
                <option value="7d">7天内</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                标签
              </label>
              <input
                type="text"
                placeholder="输入标签名称"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 placeholder-gray-500 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              />
            </div>
          </div>
          <div className="mt-4 flex justify-end space-x-2">
            <Button variant="outline" size="sm" onClick={() => setIsFilterOpen(false)}>
              取消
            </Button>
            <Button size="sm">
              应用筛选
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

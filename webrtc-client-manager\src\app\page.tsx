'use client'

import { useState } from 'react'
import ClientList from '@/components/ClientList'
import Tab<PERSON>ar, { TabType } from '@/components/TabBar'
import ClientInfo from '@/components/ClientInfo'
import RemoteDesktop from '@/components/RemoteDesktop'
import FileManager from '@/components/FileManager'
import CmdTerminal from '@/components/CmdTerminal'
import { Client } from '@/types/client'

export default function HomePage() {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [activeTab, setActiveTab] = useState<TabType>('remote')

  // 模拟客户端数据
  const mockClients: Client[] = [
    {
      id: 'client-001',
      name: 'PC-办公室-001',
      ip: '*************',
      status: 'online',
      os: 'Windows 11 Pro',
      location: '北京市 海淀区',
      uuid: '191215',
      owner: {
        name: '张三',
        uuid: 'a50f8f4a8a6d4c6a2a1f0b6e'
      },
      group: '办公',
      lastSeen: '刚刚',
      version: '2.1.0',
      cpu: {
        usage: 45,
        model: 'Intel Core i7-11700'
      },
      memory: {
        usage: 68,
        total: 16 * 1024 * 1024 * 1024, // 16GB
        available: 5 * 1024 * 1024 * 1024 // 5GB
      },
      disk: {
        usage: 75,
        total: 500 * 1024 * 1024 * 1024, // 500GB
        available: 125 * 1024 * 1024 * 1024 // 125GB
      },
      network: {
        upload: 1024 * 50, // 50KB/s
        download: 1024 * 200 // 200KB/s
      }
    },
    {
      id: 'client-002',
      name: 'PC-财务-002',
      ip: '*************',
      status: 'online',
      os: 'Windows 10 Pro',
      location: '北京市 朝阳区',
      uuid: '191216',
      owner: {
        name: '李四',
        uuid: 'b60f8f4a8a6d4c6a2a1f0b6f'
      },
      group: '财务',
      lastSeen: '2分钟前',
      version: '2.0.8',
      cpu: {
        usage: 32,
        model: 'Intel Core i5-10400'
      },
      memory: {
        usage: 55,
        total: 8 * 1024 * 1024 * 1024, // 8GB
        available: 3.6 * 1024 * 1024 * 1024 // 3.6GB
      },
      disk: {
        usage: 60,
        total: 256 * 1024 * 1024 * 1024, // 256GB
        available: 102 * 1024 * 1024 * 1024 // 102GB
      },
      network: {
        upload: 1024 * 25, // 25KB/s
        download: 1024 * 150 // 150KB/s
      }
    },
    {
      id: 'client-003',
      name: 'PC-技术-003',
      ip: '*************',
      status: 'offline',
      os: 'Windows 11 Pro',
      location: '上海市 浦东新区',
      uuid: '191217',
      owner: {
        name: '王五',
        uuid: 'c70f8f4a8a6d4c6a2a1f0b70'
      },
      group: '技术',
      lastSeen: '1小时前',
      version: '2.1.0',
      cpu: {
        usage: 0,
        model: 'AMD Ryzen 7 5700X'
      },
      memory: {
        usage: 0,
        total: 32 * 1024 * 1024 * 1024, // 32GB
        available: 32 * 1024 * 1024 * 1024 // 32GB
      },
      disk: {
        usage: 45,
        total: 1024 * 1024 * 1024 * 1024, // 1TB
        available: 563 * 1024 * 1024 * 1024 // 563GB
      },
      network: {
        upload: 0,
        download: 0
      }
    },
    {
      id: 'client-004',
      name: 'PC-销售-004',
      ip: '*************',
      status: 'online',
      os: 'Windows 10 Enterprise',
      location: '广州市 天河区',
      uuid: '191218',
      owner: {
        name: '赵六',
        uuid: 'd80f8f4a8a6d4c6a2a1f0b71'
      },
      group: '销售',
      lastSeen: '刚刚',
      version: '2.0.9',
      cpu: {
        usage: 78,
        model: 'Intel Core i5-9400'
      },
      memory: {
        usage: 82,
        total: 16 * 1024 * 1024 * 1024, // 16GB
        available: 2.9 * 1024 * 1024 * 1024 // 2.9GB
      },
      disk: {
        usage: 85,
        total: 512 * 1024 * 1024 * 1024, // 512GB
        available: 77 * 1024 * 1024 * 1024 // 77GB
      },
      network: {
        upload: 1024 * 80, // 80KB/s
        download: 1024 * 300 // 300KB/s
      }
    },
    {
      id: 'client-005',
      name: 'PC-人事-005',
      ip: '*************',
      status: 'online',
      os: 'Windows 11 Pro',
      location: '深圳市 南山区',
      uuid: '191219',
      owner: {
        name: '孙七',
        uuid: 'e90f8f4a8a6d4c6a2a1f0b72'
      },
      group: '人事',
      lastSeen: '5分钟前',
      version: '2.1.0',
      cpu: {
        usage: 25,
        model: 'Intel Core i7-12700'
      },
      memory: {
        usage: 42,
        total: 16 * 1024 * 1024 * 1024, // 16GB
        available: 9.3 * 1024 * 1024 * 1024 // 9.3GB
      },
      disk: {
        usage: 55,
        total: 500 * 1024 * 1024 * 1024, // 500GB
        available: 225 * 1024 * 1024 * 1024 // 225GB
      },
      network: {
        upload: 1024 * 15, // 15KB/s
        download: 1024 * 100 // 100KB/s
      }
    }
  ]

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client)
  }

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab)
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'remote':
        return <RemoteDesktop client={selectedClient} />
      case 'files':
        return <FileManager client={selectedClient} />
      case 'cmd':
        return <CmdTerminal client={selectedClient} />
      default:
        return <RemoteDesktop client={selectedClient} />
    }
  }

  return (
    <div className="h-screen flex bg-gray-100">
      {/* 左侧客户端列表 */}
      <ClientList
        clients={mockClients}
        selectedClient={selectedClient}
        onClientSelect={handleClientSelect}
      />

      {/* 右侧主要内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部选项卡 */}
        <TabBar
          activeTab={activeTab}
          onTabChange={handleTabChange}
          disabled={!selectedClient}
        />

        {/* 客户端详细信息 */}
        {selectedClient && (
          <ClientInfo client={selectedClient} />
        )}

        {/* 主要内容区域 */}
        <div className="flex-1 overflow-hidden">
          {renderTabContent()}
        </div>
      </div>
    </div>
  )
}

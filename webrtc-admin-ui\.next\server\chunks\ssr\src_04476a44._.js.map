{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', loading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'\n    \n    const variants = {\n      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'\n    }\n    \n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base'\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        disabled={disabled || loading}\n        ref={ref}\n        {...props}\n      >\n        {loading && (\n          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport default Button\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACvF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,UAAU,YAAY;QACtB,KAAK;QACJ,GAAG,KAAK;;YAER,yBACC,8OAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/app/settings/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { \n  Save, \n  RefreshCw, \n  Settings, \n  Shield, \n  Monitor, \n  Wifi,\n  Lock,\n  Key,\n  Clock,\n  Users,\n  AlertTriangle\n} from 'lucide-react'\nimport { SystemSettings } from '@/types'\nimport Button from '@/components/ui/Button'\n\n// 模拟系统设置数据\nconst mockSettings: SystemSettings = {\n  webrtc: {\n    stunServers: ['stun:stun.l.google.com:19302', 'stun:stun1.l.google.com:19302'],\n    turnServers: [\n      {\n        urls: 'turn:turn.example.com:3478',\n        username: 'turnuser',\n        credential: 'turnpass'\n      }\n    ]\n  },\n  security: {\n    authMethod: 'both',\n    sessionTimeout: 3600,\n    maxConcurrentSessions: 5\n  },\n  display: {\n    defaultResolution: '1920x1080',\n    defaultQuality: 'high',\n    enableAudio: true\n  }\n}\n\nexport default function SettingsPage() {\n  const [settings, setSettings] = useState<SystemSettings>(mockSettings)\n  const [activeTab, setActiveTab] = useState('webrtc')\n  const [hasChanges, setHasChanges] = useState(false)\n\n  const tabs = [\n    { id: 'webrtc', label: 'WebRTC 配置', icon: Wifi },\n    { id: 'security', label: '安全设置', icon: Shield },\n    { id: 'display', label: '显示设置', icon: Monitor },\n    { id: 'system', label: '系统设置', icon: Settings }\n  ]\n\n  const handleSave = () => {\n    console.log('保存设置:', settings)\n    setHasChanges(false)\n    // TODO: 实现保存逻辑\n  }\n\n  const handleReset = () => {\n    setSettings(mockSettings)\n    setHasChanges(false)\n  }\n\n  const updateSettings = (section: keyof SystemSettings, field: string, value: any) => {\n    setSettings(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }))\n    setHasChanges(true)\n  }\n\n  const addStunServer = () => {\n    const newServer = prompt('请输入 STUN 服务器地址:')\n    if (newServer) {\n      setSettings(prev => ({\n        ...prev,\n        webrtc: {\n          ...prev.webrtc,\n          stunServers: [...prev.webrtc.stunServers, newServer]\n        }\n      }))\n      setHasChanges(true)\n    }\n  }\n\n  const removeStunServer = (index: number) => {\n    setSettings(prev => ({\n      ...prev,\n      webrtc: {\n        ...prev.webrtc,\n        stunServers: prev.webrtc.stunServers.filter((_, i) => i !== index)\n      }\n    }))\n    setHasChanges(true)\n  }\n\n  const addTurnServer = () => {\n    setSettings(prev => ({\n      ...prev,\n      webrtc: {\n        ...prev.webrtc,\n        turnServers: [...prev.webrtc.turnServers, {\n          urls: '',\n          username: '',\n          credential: ''\n        }]\n      }\n    }))\n    setHasChanges(true)\n  }\n\n  const updateTurnServer = (index: number, field: string, value: string) => {\n    setSettings(prev => ({\n      ...prev,\n      webrtc: {\n        ...prev.webrtc,\n        turnServers: prev.webrtc.turnServers.map((server, i) => \n          i === index ? { ...server, [field]: value } : server\n        )\n      }\n    }))\n    setHasChanges(true)\n  }\n\n  const removeTurnServer = (index: number) => {\n    setSettings(prev => ({\n      ...prev,\n      webrtc: {\n        ...prev.webrtc,\n        turnServers: prev.webrtc.turnServers.filter((_, i) => i !== index)\n      }\n    }))\n    setHasChanges(true)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 页面标题 */}\n      <div className=\"bg-white rounded-lg shadow p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-xl font-semibold text-gray-900\">系统设置</h1>\n            <p className=\"text-sm text-gray-500 mt-1\">配置系统参数和安全选项</p>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {hasChanges && (\n              <div className=\"flex items-center text-yellow-600 text-sm mr-4\">\n                <AlertTriangle className=\"h-4 w-4 mr-1\" />\n                有未保存的更改\n              </div>\n            )}\n            <Button variant=\"outline\" size=\"sm\" onClick={handleReset}>\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\n              重置\n            </Button>\n            <Button size=\"sm\" onClick={handleSave} disabled={!hasChanges}>\n              <Save className=\"h-4 w-4 mr-2\" />\n              保存设置\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex space-x-6\">\n        {/* 左侧标签页 */}\n        <div className=\"w-64\">\n          <nav className=\"space-y-1\">\n            {tabs.map(tab => {\n              const Icon = tab.icon\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\n                    activeTab === tab.id\n                      ? 'bg-blue-100 text-blue-700'\n                      : 'text-gray-600 hover:bg-gray-100'\n                  }`}\n                >\n                  <Icon className=\"h-5 w-5 mr-3\" />\n                  {tab.label}\n                </button>\n              )\n            })}\n          </nav>\n        </div>\n\n        {/* 右侧设置内容 */}\n        <div className=\"flex-1\">\n          {/* WebRTC 配置 */}\n          {activeTab === 'webrtc' && (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-medium text-gray-900 mb-6\">WebRTC 配置</h2>\n              \n              {/* STUN 服务器 */}\n              <div className=\"mb-8\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h3 className=\"text-sm font-medium text-gray-900\">STUN 服务器</h3>\n                  <Button size=\"sm\" variant=\"outline\" onClick={addStunServer}>\n                    添加服务器\n                  </Button>\n                </div>\n                <div className=\"space-y-2\">\n                  {settings.webrtc.stunServers.map((server, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"text\"\n                        value={server}\n                        onChange={(e) => {\n                          const newServers = [...settings.webrtc.stunServers]\n                          newServers[index] = e.target.value\n                          updateSettings('webrtc', 'stunServers', newServers)\n                        }}\n                        className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                      />\n                      <Button \n                        size=\"sm\" \n                        variant=\"outline\" \n                        onClick={() => removeStunServer(index)}\n                      >\n                        删除\n                      </Button>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* TURN 服务器 */}\n              <div>\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h3 className=\"text-sm font-medium text-gray-900\">TURN 服务器</h3>\n                  <Button size=\"sm\" variant=\"outline\" onClick={addTurnServer}>\n                    添加服务器\n                  </Button>\n                </div>\n                <div className=\"space-y-4\">\n                  {settings.webrtc.turnServers.map((server, index) => (\n                    <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                        <div>\n                          <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                            服务器地址\n                          </label>\n                          <input\n                            type=\"text\"\n                            value={server.urls}\n                            onChange={(e) => updateTurnServer(index, 'urls', e.target.value)}\n                            placeholder=\"turn:example.com:3478\"\n                            className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                            用户名\n                          </label>\n                          <input\n                            type=\"text\"\n                            value={server.username}\n                            onChange={(e) => updateTurnServer(index, 'username', e.target.value)}\n                            className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-xs font-medium text-gray-700 mb-1\">\n                            密码\n                          </label>\n                          <div className=\"flex space-x-2\">\n                            <input\n                              type=\"password\"\n                              value={server.credential}\n                              onChange={(e) => updateTurnServer(index, 'credential', e.target.value)}\n                              className=\"flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                            />\n                            <Button \n                              size=\"sm\" \n                              variant=\"outline\" \n                              onClick={() => removeTurnServer(index)}\n                            >\n                              删除\n                            </Button>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 安全设置 */}\n          {activeTab === 'security' && (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-medium text-gray-900 mb-6\">安全设置</h2>\n              \n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    <Lock className=\"h-4 w-4 inline mr-2\" />\n                    认证方式\n                  </label>\n                  <select\n                    value={settings.security.authMethod}\n                    onChange={(e) => updateSettings('security', 'authMethod', e.target.value)}\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                  >\n                    <option value=\"password\">密码认证</option>\n                    <option value=\"key\">密钥认证</option>\n                    <option value=\"both\">密码 + 密钥</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    <Clock className=\"h-4 w-4 inline mr-2\" />\n                    会话超时时间 (秒)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={settings.security.sessionTimeout}\n                    onChange={(e) => updateSettings('security', 'sessionTimeout', parseInt(e.target.value))}\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    <Users className=\"h-4 w-4 inline mr-2\" />\n                    最大并发会话数\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={settings.security.maxConcurrentSessions}\n                    onChange={(e) => updateSettings('security', 'maxConcurrentSessions', parseInt(e.target.value))}\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                  />\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 显示设置 */}\n          {activeTab === 'display' && (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-medium text-gray-900 mb-6\">显示设置</h2>\n              \n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    默认分辨率\n                  </label>\n                  <select\n                    value={settings.display.defaultResolution}\n                    onChange={(e) => updateSettings('display', 'defaultResolution', e.target.value)}\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                  >\n                    <option value=\"1920x1080\">1920x1080 (Full HD)</option>\n                    <option value=\"1366x768\">1366x768 (HD)</option>\n                    <option value=\"1024x768\">1024x768 (XGA)</option>\n                    <option value=\"800x600\">800x600 (SVGA)</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    默认画质\n                  </label>\n                  <select\n                    value={settings.display.defaultQuality}\n                    onChange={(e) => updateSettings('display', 'defaultQuality', e.target.value)}\n                    className=\"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none\"\n                  >\n                    <option value=\"high\">高质量</option>\n                    <option value=\"medium\">中等质量</option>\n                    <option value=\"low\">低质量</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={settings.display.enableAudio}\n                      onChange={(e) => updateSettings('display', 'enableAudio', e.target.checked)}\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2\"\n                    />\n                    <span className=\"text-sm font-medium text-gray-700\">启用音频传输</span>\n                  </label>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 系统设置 */}\n          {activeTab === 'system' && (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-medium text-gray-900 mb-6\">系统设置</h2>\n              \n              <div className=\"space-y-6\">\n                <div className=\"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n                  <div className=\"flex items-center\">\n                    <AlertTriangle className=\"h-5 w-5 text-yellow-600 mr-2\" />\n                    <span className=\"text-sm font-medium text-yellow-800\">\n                      系统维护功能\n                    </span>\n                  </div>\n                  <p className=\"text-sm text-yellow-700 mt-1\">\n                    这些操作可能会影响系统运行，请谨慎操作。\n                  </p>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <Button variant=\"outline\">\n                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                    重启系统服务\n                  </Button>\n                  \n                  <Button variant=\"outline\">\n                    <Save className=\"h-4 w-4 mr-2\" />\n                    备份系统配置\n                  </Button>\n                  \n                  <Button variant=\"outline\">\n                    <Key className=\"h-4 w-4 mr-2\" />\n                    重新生成密钥\n                  </Button>\n                  \n                  <Button variant=\"danger\">\n                    <AlertTriangle className=\"h-4 w-4 mr-2\" />\n                    恢复出厂设置\n                  </Button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAjBA;;;;;AAmBA,WAAW;AACX,MAAM,eAA+B;IACnC,QAAQ;QACN,aAAa;YAAC;YAAgC;SAAgC;QAC9E,aAAa;YACX;gBACE,MAAM;gBACN,UAAU;gBACV,YAAY;YACd;SACD;IACH;IACA,UAAU;QACR,YAAY;QACZ,gBAAgB;QAChB,uBAAuB;IACzB;IACA,SAAS;QACP,mBAAmB;QACnB,gBAAgB;QAChB,aAAa;IACf;AACF;AAEe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,OAAO;QACX;YAAE,IAAI;YAAU,OAAO;YAAa,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC/C;YAAE,IAAI;YAAY,OAAO;YAAQ,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC9C;YAAE,IAAI;YAAW,OAAO;YAAQ,MAAM,wMAAA,CAAA,UAAO;QAAC;QAC9C;YAAE,IAAI;YAAU,OAAO;YAAQ,MAAM,0MAAA,CAAA,WAAQ;QAAC;KAC/C;IAED,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,SAAS;QACrB,cAAc;IACd,eAAe;IACjB;IAEA,MAAM,cAAc;QAClB,YAAY;QACZ,cAAc;IAChB;IAEA,MAAM,iBAAiB,CAAC,SAA+B,OAAe;QACpE,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;oBACT,GAAG,IAAI,CAAC,QAAQ;oBAChB,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;QACD,cAAc;IAChB;IAEA,MAAM,gBAAgB;QACpB,MAAM,YAAY,OAAO;QACzB,IAAI,WAAW;YACb,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,QAAQ;wBACN,GAAG,KAAK,MAAM;wBACd,aAAa;+BAAI,KAAK,MAAM,CAAC,WAAW;4BAAE;yBAAU;oBACtD;gBACF,CAAC;YACD,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ;oBACN,GAAG,KAAK,MAAM;oBACd,aAAa,KAAK,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gBAC9D;YACF,CAAC;QACD,cAAc;IAChB;IAEA,MAAM,gBAAgB;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ;oBACN,GAAG,KAAK,MAAM;oBACd,aAAa;2BAAI,KAAK,MAAM,CAAC,WAAW;wBAAE;4BACxC,MAAM;4BACN,UAAU;4BACV,YAAY;wBACd;qBAAE;gBACJ;YACF,CAAC;QACD,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC,OAAe,OAAe;QACtD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ;oBACN,GAAG,KAAK,MAAM;oBACd,aAAa,KAAK,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,IAChD,MAAM,QAAQ;4BAAE,GAAG,MAAM;4BAAE,CAAC,MAAM,EAAE;wBAAM,IAAI;gBAElD;YACF,CAAC;QACD,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,QAAQ;oBACN,GAAG,KAAK,MAAM;oBACd,aAAa,KAAK,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gBAC9D;YACF,CAAC;QACD,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAE5C,8OAAC;4BAAI,WAAU;;gCACZ,4BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAI9C,8OAAC,kIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS;;sDAC3C,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,8OAAC,kIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAY,UAAU,CAAC;;sDAChD,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAA;gCACR,MAAM,OAAO,IAAI,IAAI;gCACrB,qBACE,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,8BACA,mCACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCATL,IAAI,EAAE;;;;;4BAYjB;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;4BAEZ,cAAc,0BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC,kIAAA,CAAA,UAAM;wDAAC,MAAK;wDAAK,SAAQ;wDAAU,SAAS;kEAAe;;;;;;;;;;;;0DAI9D,8OAAC;gDAAI,WAAU;0DACZ,SAAS,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACxC,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC;oEACT,MAAM,aAAa;2EAAI,SAAS,MAAM,CAAC,WAAW;qEAAC;oEACnD,UAAU,CAAC,MAAM,GAAG,EAAE,MAAM,CAAC,KAAK;oEAClC,eAAe,UAAU,eAAe;gEAC1C;gEACA,WAAU;;;;;;0EAEZ,8OAAC,kIAAA,CAAA,UAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,iBAAiB;0EACjC;;;;;;;uDAfO;;;;;;;;;;;;;;;;kDAwBhB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC,kIAAA,CAAA,UAAM;wDAAC,MAAK;wDAAK,SAAQ;wDAAU,SAAS;kEAAe;;;;;;;;;;;;0DAI9D,8OAAC;gDAAI,WAAU;0DACZ,SAAS,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACxC,8OAAC;wDAAgB,WAAU;kEACzB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAGhE,8OAAC;4EACC,MAAK;4EACL,OAAO,OAAO,IAAI;4EAClB,UAAU,CAAC,IAAM,iBAAiB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;4EAC/D,aAAY;4EACZ,WAAU;;;;;;;;;;;;8EAGd,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAGhE,8OAAC;4EACC,MAAK;4EACL,OAAO,OAAO,QAAQ;4EACtB,UAAU,CAAC,IAAM,iBAAiB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;4EACnE,WAAU;;;;;;;;;;;;8EAGd,8OAAC;;sFACC,8OAAC;4EAAM,WAAU;sFAA+C;;;;;;sFAGhE,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFACC,MAAK;oFACL,OAAO,OAAO,UAAU;oFACxB,UAAU,CAAC,IAAM,iBAAiB,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;oFACrE,WAAU;;;;;;8FAEZ,8OAAC,kIAAA,CAAA,UAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,SAAS,IAAM,iBAAiB;8FACjC;;;;;;;;;;;;;;;;;;;;;;;;uDAxCC;;;;;;;;;;;;;;;;;;;;;;4BAsDnB,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAEvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;0EACf,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAwB;;;;;;;kEAG1C,8OAAC;wDACC,OAAO,SAAS,QAAQ,CAAC,UAAU;wDACnC,UAAU,CAAC,IAAM,eAAe,YAAY,cAAc,EAAE,MAAM,CAAC,KAAK;wDACxE,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,8OAAC;gEAAO,OAAM;0EAAO;;;;;;;;;;;;;;;;;;0DAIzB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;0EACf,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAwB;;;;;;;kEAG3C,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,QAAQ,CAAC,cAAc;wDACvC,UAAU,CAAC,IAAM,eAAe,YAAY,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wDACrF,WAAU;;;;;;;;;;;;0DAId,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;;0EACf,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAwB;;;;;;;kEAG3C,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,QAAQ,CAAC,qBAAqB;wDAC9C,UAAU,CAAC,IAAM,eAAe,YAAY,yBAAyB,SAAS,EAAE,MAAM,CAAC,KAAK;wDAC5F,WAAU;;;;;;;;;;;;;;;;;;;;;;;;4BAQnB,cAAc,2BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAEvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,SAAS,OAAO,CAAC,iBAAiB;wDACzC,UAAU,CAAC,IAAM,eAAe,WAAW,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDAC9E,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAY;;;;;;0EAC1B,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAU;;;;;;;;;;;;;;;;;;0DAI5B,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,SAAS,OAAO,CAAC,cAAc;wDACtC,UAAU,CAAC,IAAM,eAAe,WAAW,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDAC3E,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;;;;;;;;;;;;;0DAIxB,8OAAC;0DACC,cAAA,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,SAAS,OAAO,CAAC,WAAW;4DACrC,UAAU,CAAC,IAAM,eAAe,WAAW,eAAe,EAAE,MAAM,CAAC,OAAO;4DAC1E,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ7D,cAAc,0BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAEvD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;0EACzB,8OAAC;gEAAK,WAAU;0EAAsC;;;;;;;;;;;;kEAIxD,8OAAC;wDAAE,WAAU;kEAA+B;;;;;;;;;;;;0DAK9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,UAAM;wDAAC,SAAQ;;0EACd,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAIxC,8OAAC,kIAAA,CAAA,UAAM;wDAAC,SAAQ;;0EACd,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAInC,8OAAC,kIAAA,CAAA,UAAM;wDAAC,SAAQ;;0EACd,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAIlC,8OAAC,kIAAA,CAAA,UAAM;wDAAC,SAAQ;;0EACd,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9D", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface BadgeProps extends HTMLAttributes<HTMLSpanElement> {\n  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info'\n  size?: 'xs' | 'sm' | 'md'\n}\n\nexport default function Badge({ \n  className, \n  variant = 'default', \n  size = 'md', \n  children, \n  ...props \n}: BadgeProps) {\n  const baseClasses = 'inline-flex items-center font-medium rounded-full border'\n  \n  const variants = {\n    default: 'bg-gray-100 text-gray-800 border-gray-200',\n    success: 'bg-green-100 text-green-800 border-green-200',\n    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',\n    danger: 'bg-red-100 text-red-800 border-red-200',\n    info: 'bg-blue-100 text-blue-800 border-blue-200'\n  }\n  \n  const sizes = {\n    xs: 'px-1.5 py-0.5 text-xs',\n    sm: 'px-2 py-0.5 text-xs',\n    md: 'px-2.5 py-1 text-sm'\n  }\n\n  return (\n    <span\n      className={cn(\n        baseClasses,\n        variants[variant],\n        sizes[size],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAOe,SAAS,MAAM,EAC5B,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACQ;IACX,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/components/remote/ClientSelector.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo } from 'react'\nimport { Monitor, ChevronDown, Wifi, Search, X } from 'lucide-react'\nimport Badge from '@/components/ui/Badge'\n\ninterface Client {\n  id: string\n  name: string\n  ip: string\n  status: 'online' | 'offline'\n  os: string\n  location: string\n  uuid: string\n  owner: {\n    name: string\n    uuid: string\n  }\n  group: string\n}\n\ninterface ClientSelectorProps {\n  selectedClient: Client | null\n  onClientSelect: (client: Client) => void\n}\n\nconst mockClients: Client[] = [\n  {\n    id: 'client-001',\n    name: 'PC-办公室-001',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    location: '北京市 海淀区',\n    uuid: '191215',\n    owner: {\n      name: '张三',\n      uuid: 'a50f8f4a8a6d4c6a2a1f0b6e'\n    },\n    group: '办公'\n  },\n  {\n    id: 'client-002',\n    name: 'PC-会议室-020',\n    ip: '*************',\n    status: 'online',\n    os: 'Ubuntu 18.04',\n    location: '深圳市 南山区',\n    uuid: '220806',\n    owner: {\n      name: '李四',\n      uuid: 'a6d56211bec18f3f43aa'\n    },\n    group: '会议'\n  },\n  {\n    id: 'client-003',\n    name: 'PC-财务-004',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    location: '深圳市 南山区',\n    uuid: '060815',\n    owner: {\n      name: '王五',\n      uuid: 'e4a827f60c4e4a2d6f0b'\n    },\n    group: '财务'\n  },\n  {\n    id: 'client-004',\n    name: 'PC-测试-015',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    location: '深圳市 南山区',\n    uuid: '170901',\n    owner: {\n      name: '赵六',\n      uuid: 'e2a60f4c8e2a6d0f4c6e'\n    },\n    group: '开发'\n  },\n  {\n    id: 'client-005',\n    name: 'PC-开发-008',\n    ip: '*************',\n    status: 'online',\n    os: 'macOS Sonoma',\n    location: '上海市 浦东新区',\n    uuid: '081229',\n    owner: {\n      name: '孙七',\n      uuid: 'f6c4e8a2d6f0b4c8e2a6'\n    },\n    group: '开发'\n  },\n  {\n    id: 'client-006',\n    name: 'PC-设计-012',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 11 Pro',\n    location: '广州市 天河区',\n    uuid: '050613',\n    owner: {\n      name: '周八',\n      uuid: 'a6d56211bec18f3f43bb'\n    },\n    group: '设计'\n  },\n  {\n    id: 'client-007',\n    name: 'PC-销售-003',\n    ip: '*************',\n    status: 'online',\n    os: 'Windows 10 Pro',\n    location: '北京市 朝阳区',\n    uuid: '050815',\n    owner: {\n      name: '吴九',\n      uuid: 'c4e8a2d6f0b4c8e2a6d0'\n    },\n    group: '销售'\n  },\n  {\n    id: 'client-008',\n    name: 'PC-运维-009',\n    ip: '*************',\n    status: 'online',\n    os: 'CentOS 8',\n    location: '深圳市 福田区',\n    uuid: '081229',\n    owner: {\n      name: '郑十',\n      uuid: 'f6c4e8a2d6f0b4c8e2a7'\n    },\n    group: '运维'\n  }\n]\n\nexport default function ClientSelector({ selectedClient, onClientSelect }: ClientSelectorProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedGroups, setSelectedGroups] = useState<string[]>([])\n\n  // 只显示在线客户端\n  const onlineClients = mockClients.filter(client => client.status === 'online')\n\n  // 获取所有分组\n  const allGroups = useMemo(() => {\n    const groups = new Set<string>()\n    onlineClients.forEach(client => {\n      groups.add(client.group)\n    })\n    return Array.from(groups).sort()\n  }, [onlineClients])\n\n  // 过滤客户端\n  const filteredClients = useMemo(() => {\n    return onlineClients.filter(client => {\n      // 搜索过滤 - 支持搜索客户端名称、归属用户、UUID\n      const matchesSearch = searchTerm === '' ||\n        client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        client.owner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        client.uuid.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        client.owner.uuid.toLowerCase().includes(searchTerm.toLowerCase())\n\n      // 分组过滤\n      const matchesGroup = selectedGroups.length === 0 ||\n        selectedGroups.includes(client.group)\n\n      return matchesSearch && matchesGroup\n    })\n  }, [onlineClients, searchTerm, selectedGroups])\n\n  const handleClientSelect = (client: Client) => {\n    onClientSelect(client)\n    setIsOpen(false)\n    setSearchTerm('')\n    setSelectedGroups([])\n  }\n\n  const handleGroupToggle = (group: string) => {\n    setSelectedGroups(prev =>\n      prev.includes(group)\n        ? prev.filter(g => g !== group)\n        : [...prev, group]\n    )\n  }\n\n  const clearSearch = () => {\n    setSearchTerm('')\n  }\n\n  const clearFilters = () => {\n    setSearchTerm('')\n    setSelectedGroups([])\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* 选择器按钮 */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center justify-between w-full min-w-80 px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n      >\n        <div className=\"flex items-center\">\n          <Monitor className=\"h-5 w-5 text-gray-400 mr-3\" />\n          <div className=\"text-left\">\n            {selectedClient ? (\n              <>\n                <div className=\"text-sm font-medium text-gray-900\">\n                  {selectedClient.name} • {selectedClient.owner.name}\n                </div>\n                <div className=\"text-xs text-gray-500\">\n                  {selectedClient.ip} • {selectedClient.uuid}\n                </div>\n              </>\n            ) : (\n              <div className=\"text-sm text-gray-500\">选择客户端</div>\n            )}\n          </div>\n        </div>\n        <div className=\"flex items-center\">\n          {selectedClient && (\n            <Badge \n              variant={selectedClient.status === 'online' ? 'success' : 'secondary'}\n              size=\"sm\"\n              className=\"mr-2\"\n            >\n              {selectedClient.status === 'online' ? '在线' : '离线'}\n            </Badge>\n          )}\n          <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />\n        </div>\n      </button>\n\n      {/* 下拉列表 */}\n      {isOpen && (\n        <>\n          {/* 遮罩层 */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n\n          {/* 下拉菜单 */}\n          <div className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-[500px] flex flex-col\">\n            {/* 搜索框 */}\n            <div className=\"p-3 border-b border-gray-200\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索客户端名称、归属用户、UUID...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-8 py-2 border border-gray-300 rounded-md text-sm text-gray-900 placeholder-gray-500 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                {searchTerm && (\n                  <button\n                    onClick={clearSearch}\n                    className=\"absolute right-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded\"\n                  >\n                    <X className=\"h-3 w-3 text-gray-400\" />\n                  </button>\n                )}\n              </div>\n            </div>\n\n            {/* 分组筛选 */}\n            <div className=\"p-3 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-xs font-medium text-gray-700\">分组筛选</span>\n                {selectedGroups.length > 0 && (\n                  <button\n                    onClick={clearFilters}\n                    className=\"text-xs text-blue-600 hover:text-blue-800\"\n                  >\n                    清除筛选\n                  </button>\n                )}\n              </div>\n              <div className=\"flex flex-wrap gap-1\">\n                {allGroups.map(group => (\n                  <button\n                    key={group}\n                    onClick={() => handleGroupToggle(group)}\n                    className={`px-2 py-1 text-xs rounded-full border transition-colors ${\n                      selectedGroups.includes(group)\n                        ? 'bg-blue-100 border-blue-300 text-blue-700'\n                        : 'bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-100'\n                    }`}\n                  >\n                    {group}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* 客户端列表 */}\n            <div className=\"flex-1 overflow-y-auto max-h-80\">\n              <div className=\"p-2\">\n                <div className=\"flex items-center justify-between px-2 py-1 mb-2\">\n                  <span className=\"text-xs font-medium text-gray-500\">\n                    在线客户端 ({filteredClients.length})\n                  </span>\n                  {(searchTerm || selectedGroups.length > 0) && (\n                    <span className=\"text-xs text-gray-400\">\n                      共 {onlineClients.length} 台\n                    </span>\n                  )}\n                </div>\n\n                {filteredClients.length > 0 ? (\n                  filteredClients.map((client) => (\n                    <button\n                      key={client.id}\n                      onClick={() => handleClientSelect(client)}\n                      className={`w-full flex items-center px-3 py-2 rounded-md text-left hover:bg-gray-50 transition-colors ${\n                        selectedClient?.id === client.id ? 'bg-blue-50 border border-blue-200' : ''\n                      }`}\n                    >\n                      <div className=\"flex items-center flex-1\">\n                        <Wifi className=\"h-4 w-4 text-green-500 mr-3 flex-shrink-0\" />\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"text-sm font-medium text-gray-900 truncate\">\n                            {client.name} • {client.owner.name}\n                          </div>\n                          <div className=\"text-xs text-gray-500 truncate\">\n                            {client.ip} • {client.uuid}\n                          </div>\n                        </div>\n                        <Badge variant=\"success\" size=\"xs\" className=\"ml-2 flex-shrink-0\">\n                          在线\n                        </Badge>\n                      </div>\n                    </button>\n                  ))\n                ) : (\n                  <div className=\"px-3 py-8 text-center text-gray-500\">\n                    <Monitor className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\n                    <p className=\"text-sm\">未找到匹配的客户端</p>\n                    <p className=\"text-xs mt-1\">请尝试调整搜索条件或分组筛选</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AA0BA,MAAM,cAAwB;IAC5B;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;YACL,MAAM;YACN,MAAM;QACR;QACA,OAAO;IACT;CACD;AAEc,SAAS,eAAe,EAAE,cAAc,EAAE,cAAc,EAAuB;IAC5F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEjE,WAAW;IACX,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;IAErE,SAAS;IACT,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,SAAS,IAAI;QACnB,cAAc,OAAO,CAAC,CAAA;YACpB,OAAO,GAAG,CAAC,OAAO,KAAK;QACzB;QACA,OAAO,MAAM,IAAI,CAAC,QAAQ,IAAI;IAChC,GAAG;QAAC;KAAc;IAElB,QAAQ;IACR,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,OAAO,cAAc,MAAM,CAAC,CAAA;YAC1B,6BAA6B;YAC7B,MAAM,gBAAgB,eAAe,MACnC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAEjE,OAAO;YACP,MAAM,eAAe,eAAe,MAAM,KAAK,KAC7C,eAAe,QAAQ,CAAC,OAAO,KAAK;YAEtC,OAAO,iBAAiB;QAC1B;IACF,GAAG;QAAC;QAAe;QAAY;KAAe;IAE9C,MAAM,qBAAqB,CAAC;QAC1B,eAAe;QACf,UAAU;QACV,cAAc;QACd,kBAAkB,EAAE;IACtB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,CAAA,OAChB,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,SACvB;mBAAI;gBAAM;aAAM;IAExB;IAEA,MAAM,cAAc;QAClB,cAAc;IAChB;IAEA,MAAM,eAAe;QACnB,cAAc;QACd,kBAAkB,EAAE;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAI,WAAU;0CACZ,+BACC;;sDACE,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,IAAI;gDAAC;gDAAI,eAAe,KAAK,CAAC,IAAI;;;;;;;sDAEpD,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,EAAE;gDAAC;gDAAI,eAAe,IAAI;;;;;;;;iEAI9C,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAI7C,8OAAC;wBAAI,WAAU;;4BACZ,gCACC,8OAAC,iIAAA,CAAA,UAAK;gCACJ,SAAS,eAAe,MAAM,KAAK,WAAW,YAAY;gCAC1D,MAAK;gCACL,WAAU;0CAET,eAAe,MAAM,KAAK,WAAW,OAAO;;;;;;0CAGjD,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAW,CAAC,2CAA2C,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;;;;;;;;YAKrG,wBACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;wCAEX,4BACC,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAOrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;4CACnD,eAAe,MAAM,GAAG,mBACvB,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAKL,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAA,sBACb,8OAAC;gDAEC,SAAS,IAAM,kBAAkB;gDACjC,WAAW,CAAC,wDAAwD,EAClE,eAAe,QAAQ,CAAC,SACpB,8CACA,8DACJ;0DAED;+CARI;;;;;;;;;;;;;;;;0CAeb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAoC;wDAC1C,gBAAgB,MAAM;wDAAC;;;;;;;gDAEhC,CAAC,cAAc,eAAe,MAAM,GAAG,CAAC,mBACvC,8OAAC;oDAAK,WAAU;;wDAAwB;wDACnC,cAAc,MAAM;wDAAC;;;;;;;;;;;;;wCAK7B,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,uBACnB,8OAAC;gDAEC,SAAS,IAAM,mBAAmB;gDAClC,WAAW,CAAC,2FAA2F,EACrG,gBAAgB,OAAO,OAAO,EAAE,GAAG,sCAAsC,IACzE;0DAEF,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEACZ,OAAO,IAAI;wEAAC;wEAAI,OAAO,KAAK,CAAC,IAAI;;;;;;;8EAEpC,8OAAC;oEAAI,WAAU;;wEACZ,OAAO,EAAE;wEAAC;wEAAI,OAAO,IAAI;;;;;;;;;;;;;sEAG9B,8OAAC,iIAAA,CAAA,UAAK;4DAAC,SAAQ;4DAAU,MAAK;4DAAK,WAAU;sEAAqB;;;;;;;;;;;;+CAhB/D,OAAO,EAAE;;;;sEAuBlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAE,WAAU;8DAAU;;;;;;8DACvB,8OAAC;oDAAE,WAAU;8DAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhD", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/WebRTC%E8%BF%9C%E7%A8%8B%E5%AE%A2%E6%88%B7%E7%AE%A1%E7%90%86%E5%90%8E%E5%8F%B0/webrtc-admin-ui/src/app/files/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport {\n  FolderOpen,\n  File,\n  Download,\n  Upload,\n  Trash2,\n  Edit,\n  Copy,\n  Move,\n  Search,\n  RefreshCw,\n  Home,\n  ChevronRight,\n  FileText,\n  Image,\n  Video,\n  Music,\n  Archive,\n  Folder,\n  ChevronDown,\n  HardDrive,\n  Monitor,\n  Wifi,\n  ArrowLeft,\n  ArrowRight,\n  ArrowUp,\n  MoreHorizontal,\n  Grid3X3,\n  List,\n  SortAsc\n} from 'lucide-react'\nimport ClientSelector from '@/components/remote/ClientSelector'\nimport Badge from '@/components/ui/Badge'\n\ninterface Client {\n  id: string\n  name: string\n  ip: string\n  status: 'online' | 'offline'\n  os: string\n  location: string\n  uuid: string\n  owner: {\n    name: string\n    uuid: string\n  }\n  group: string\n}\n\ninterface FileItem {\n  id: string\n  name: string\n  type: 'folder' | 'file'\n  size?: number\n  modified: string\n  extension?: string\n  path: string\n  children?: FileItem[]\n}\n\ninterface TreeNode {\n  id: string\n  name: string\n  path: string\n  type: 'drive' | 'folder'\n  children?: TreeNode[]\n  expanded?: boolean\n}\n\n// 树形目录结构\nconst mockTreeData: TreeNode[] = [\n  {\n    id: 'c',\n    name: '本地磁盘 (C:)',\n    path: 'C:',\n    type: 'drive',\n    expanded: true,\n    children: [\n      {\n        id: 'users',\n        name: 'Users',\n        path: 'C:/Users',\n        type: 'folder',\n        expanded: true,\n        children: [\n          {\n            id: 'admin',\n            name: 'Administrator',\n            path: 'C:/Users/<USER>',\n            type: 'folder',\n            expanded: true,\n            children: [\n              {\n                id: 'desktop',\n                name: 'Desktop',\n                path: 'C:/Users/<USER>/Desktop',\n                type: 'folder'\n              },\n              {\n                id: 'documents',\n                name: 'Documents',\n                path: 'C:/Users/<USER>/Documents',\n                type: 'folder'\n              },\n              {\n                id: 'downloads',\n                name: 'Downloads',\n                path: 'C:/Users/<USER>/Downloads',\n                type: 'folder'\n              },\n              {\n                id: 'pictures',\n                name: 'Pictures',\n                path: 'C:/Users/<USER>/Pictures',\n                type: 'folder'\n              }\n            ]\n          }\n        ]\n      },\n      {\n        id: 'program-files',\n        name: 'Program Files',\n        path: 'C:/Program Files',\n        type: 'folder'\n      },\n      {\n        id: 'windows',\n        name: 'Windows',\n        path: 'C:/Windows',\n        type: 'folder'\n      }\n    ]\n  },\n  {\n    id: 'd',\n    name: '本地磁盘 (D:)',\n    path: 'D:',\n    type: 'drive',\n    children: [\n      {\n        id: 'data',\n        name: 'Data',\n        path: 'D:/Data',\n        type: 'folder'\n      },\n      {\n        id: 'backup',\n        name: 'Backup',\n        path: 'D:/Backup',\n        type: 'folder'\n      }\n    ]\n  }\n]\n\n// 根据路径获取文件列表的模拟数据\nconst getFilesForPath = (path: string): FileItem[] => {\n  const pathMap: { [key: string]: FileItem[] } = {\n    'C:/Users/<USER>': [\n      {\n        id: '1',\n        name: 'Desktop',\n        type: 'folder',\n        modified: '2024-01-15 14:30',\n        path: 'C:/Users/<USER>/Desktop'\n      },\n      {\n        id: '2',\n        name: 'Documents',\n        type: 'folder',\n        modified: '2024-01-15 10:20',\n        path: 'C:/Users/<USER>/Documents'\n      },\n      {\n        id: '3',\n        name: 'Downloads',\n        type: 'folder',\n        modified: '2024-01-14 16:45',\n        path: 'C:/Users/<USER>/Downloads'\n      },\n      {\n        id: '4',\n        name: 'Pictures',\n        type: 'folder',\n        modified: '2024-01-14 15:30',\n        path: 'C:/Users/<USER>/Pictures'\n      }\n    ],\n    'C:/Users/<USER>/Documents': [\n      {\n        id: '5',\n        name: 'report.pdf',\n        type: 'file',\n        size: 2048576,\n        modified: '2024-01-15 09:15',\n        extension: 'pdf',\n        path: 'C:/Users/<USER>/Documents/report.pdf'\n      },\n      {\n        id: '6',\n        name: 'presentation.pptx',\n        type: 'file',\n        size: 5242880,\n        modified: '2024-01-14 15:30',\n        extension: 'pptx',\n        path: 'C:/Users/<USER>/Documents/presentation.pptx'\n      },\n      {\n        id: '7',\n        name: 'data.xlsx',\n        type: 'file',\n        size: 1048576,\n        modified: '2024-01-13 11:20',\n        extension: 'xlsx',\n        path: 'C:/Users/<USER>/Documents/data.xlsx'\n      }\n    ],\n    'C:/Users/<USER>/Downloads': [\n      {\n        id: '8',\n        name: 'setup.exe',\n        type: 'file',\n        size: 15728640,\n        modified: '2024-01-15 16:20',\n        extension: 'exe',\n        path: 'C:/Users/<USER>/Downloads/setup.exe'\n      },\n      {\n        id: '9',\n        name: 'archive.zip',\n        type: 'file',\n        size: 8388608,\n        modified: '2024-01-14 12:45',\n        extension: 'zip',\n        path: 'C:/Users/<USER>/Downloads/archive.zip'\n      }\n    ],\n    'C:/Users/<USER>/Pictures': [\n      {\n        id: '10',\n        name: 'photo1.jpg',\n        type: 'file',\n        size: 3145728,\n        modified: '2024-01-12 18:30',\n        extension: 'jpg',\n        path: 'C:/Users/<USER>/Pictures/photo1.jpg'\n      },\n      {\n        id: '11',\n        name: 'screenshot.png',\n        type: 'file',\n        size: 1572864,\n        modified: '2024-01-13 09:15',\n        extension: 'png',\n        path: 'C:/Users/<USER>/Pictures/screenshot.png'\n      }\n    ]\n  }\n\n  return pathMap[path] || []\n}\n\nexport default function FilesPage() {\n  const [selectedClient, setSelectedClient] = useState<Client | null>(null)\n  const [currentPath, setCurrentPath] = useState('C:/Users/<USER>')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedFiles, setSelectedFiles] = useState<string[]>([])\n  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')\n  const [treeData, setTreeData] = useState<TreeNode[]>(mockTreeData)\n  const [navigationHistory, setNavigationHistory] = useState<string[]>(['C:/Users/<USER>'])\n  const [historyIndex, setHistoryIndex] = useState(0)\n\n  // 导航相关函数\n  const navigateToPath = (path: string) => {\n    setCurrentPath(path)\n    const newHistory = navigationHistory.slice(0, historyIndex + 1)\n    newHistory.push(path)\n    setNavigationHistory(newHistory)\n    setHistoryIndex(newHistory.length - 1)\n  }\n\n  const goBack = () => {\n    if (historyIndex > 0) {\n      setHistoryIndex(historyIndex - 1)\n      setCurrentPath(navigationHistory[historyIndex - 1])\n    }\n  }\n\n  const goForward = () => {\n    if (historyIndex < navigationHistory.length - 1) {\n      setHistoryIndex(historyIndex + 1)\n      setCurrentPath(navigationHistory[historyIndex + 1])\n    }\n  }\n\n  const goUp = () => {\n    const pathParts = currentPath.split('/').filter(Boolean)\n    if (pathParts.length > 1) {\n      const parentPath = pathParts.slice(0, -1).join('/')\n      navigateToPath(parentPath.startsWith('C:') || parentPath.startsWith('D:') ? parentPath : 'C:' + parentPath)\n    }\n  }\n\n  // 树形目录相关函数\n  const toggleTreeNode = (nodeId: string) => {\n    const updateNode = (nodes: TreeNode[]): TreeNode[] => {\n      return nodes.map(node => {\n        if (node.id === nodeId) {\n          return { ...node, expanded: !node.expanded }\n        }\n        if (node.children) {\n          return { ...node, children: updateNode(node.children) }\n        }\n        return node\n      })\n    }\n    setTreeData(updateNode(treeData))\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 B'\n    const k = 1024\n    const sizes = ['B', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  const getFileIcon = (item: FileItem) => {\n    if (item.type === 'folder') {\n      return <Folder className=\"h-4 w-4 text-blue-600\" />\n    }\n\n    const ext = item.extension?.toLowerCase()\n    switch (ext) {\n      case 'pdf':\n      case 'doc':\n      case 'docx':\n      case 'txt':\n        return <FileText className=\"h-4 w-4 text-red-500\" />\n      case 'jpg':\n      case 'jpeg':\n      case 'png':\n      case 'gif':\n        return <Image className=\"h-4 w-4 text-green-500\" />\n      case 'mp4':\n      case 'avi':\n      case 'mov':\n        return <Video className=\"h-4 w-4 text-purple-500\" />\n      case 'mp3':\n      case 'wav':\n      case 'flac':\n        return <Music className=\"h-4 w-4 text-orange-500\" />\n      case 'zip':\n      case 'rar':\n      case '7z':\n        return <Archive className=\"h-4 w-4 text-yellow-500\" />\n      case 'exe':\n        return <File className=\"h-4 w-4 text-blue-500\" />\n      default:\n        return <File className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getTreeIcon = (node: TreeNode) => {\n    if (node.type === 'drive') {\n      return <HardDrive className=\"h-4 w-4 text-gray-600\" />\n    }\n    return <Folder className=\"h-4 w-4 text-blue-600\" />\n  }\n\n  // 获取当前路径的文件列表\n  const currentFiles = getFilesForPath(currentPath)\n  const filteredFiles = currentFiles.filter(file =>\n    file.name.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  // 路径分段处理\n  const pathSegments = currentPath.split(/[:/\\\\]/).filter(Boolean)\n\n  // 渲染树形节点\n  const renderTreeNode = (node: TreeNode, level = 0) => (\n    <div key={node.id}>\n      <div\n        className={`flex items-center py-1 px-2 hover:bg-gray-100 cursor-pointer text-sm ${\n          currentPath === node.path ? 'bg-blue-50 text-blue-700' : 'text-gray-700'\n        }`}\n        style={{ paddingLeft: `${level * 16 + 8}px` }}\n        onClick={() => {\n          if (node.type === 'folder') {\n            navigateToPath(node.path)\n          } else {\n            navigateToPath(node.path)\n          }\n        }}\n      >\n        {node.children && (\n          <button\n            onClick={(e) => {\n              e.stopPropagation()\n              toggleTreeNode(node.id)\n            }}\n            className=\"mr-1 p-0.5 hover:bg-gray-200 rounded\"\n          >\n            <ChevronRight\n              className={`h-3 w-3 transition-transform ${\n                node.expanded ? 'rotate-90' : ''\n              }`}\n            />\n          </button>\n        )}\n        {!node.children && <div className=\"w-4\" />}\n        {getTreeIcon(node)}\n        <span className=\"ml-2 truncate\">{node.name}</span>\n      </div>\n      {node.expanded && node.children && (\n        <div>\n          {node.children.map(child => renderTreeNode(child, level + 1))}\n        </div>\n      )}\n    </div>\n  )\n\n  return (\n    <div className=\"h-screen flex flex-col bg-white\">\n      {/* 顶部标题栏 */}\n      <div className=\"bg-white border-b border-gray-200 px-4 py-2\">\n        <div className=\"flex items-center\">\n          <h1 className=\"text-lg font-semibold text-gray-900\">文件管理</h1>\n        </div>\n      </div>\n\n      {/* 客户端选择器 - 始终显示 */}\n      <div className=\"bg-white border-b border-gray-200 p-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* 左侧：客户端选择器 */}\n          <div className=\"flex items-center space-x-4\">\n            <ClientSelector\n              selectedClient={selectedClient}\n              onClientSelect={setSelectedClient}\n            />\n            {selectedClient && (\n              <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                <span>连接状态: 已连接</span>\n                <span>文件系统: {selectedClient.os.includes('Windows') ? 'NTFS' : 'EXT4'}</span>\n              </div>\n            )}\n          </div>\n\n          {/* 右侧：操作按钮 */}\n          <div className=\"flex items-center space-x-2\">\n            {selectedClient && (\n              <>\n                <button className=\"px-3 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 flex items-center\">\n                  <Upload className=\"h-4 w-4 mr-1\" />\n                  上传\n                </button>\n                <button className=\"px-3 py-1.5 bg-green-600 text-white rounded text-sm hover:bg-green-700 flex items-center\">\n                  <FolderOpen className=\"h-4 w-4 mr-1\" />\n                  新建文件夹\n                </button>\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* 工具栏 */}\n      {selectedClient && (\n        <div className=\"bg-gray-50 border-b border-gray-200 px-4 py-2\">\n          <div className=\"flex items-center justify-between\">\n            {/* 导航按钮 */}\n            <div className=\"flex items-center space-x-1\">\n              <button\n                onClick={goBack}\n                disabled={historyIndex <= 0}\n                className=\"p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <ArrowLeft className=\"h-4 w-4\" />\n              </button>\n              <button\n                onClick={goForward}\n                disabled={historyIndex >= navigationHistory.length - 1}\n                className=\"p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <ArrowRight className=\"h-4 w-4\" />\n              </button>\n              <button\n                onClick={goUp}\n                className=\"p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded\"\n              >\n                <ArrowUp className=\"h-4 w-4\" />\n              </button>\n              <button className=\"p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded\">\n                <RefreshCw className=\"h-4 w-4\" />\n              </button>\n            </div>\n\n            {/* 视图切换和其他工具 */}\n            <div className=\"flex items-center space-x-1\">\n              <button\n                onClick={() => setViewMode('list')}\n                className={`p-1.5 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-200'}`}\n              >\n                <List className=\"h-4 w-4\" />\n              </button>\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`p-1.5 rounded ${viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-200'}`}\n              >\n                <Grid3X3 className=\"h-4 w-4\" />\n              </button>\n              <button className=\"p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded\">\n                <SortAsc className=\"h-4 w-4\" />\n              </button>\n              <button className=\"p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded\">\n                <MoreHorizontal className=\"h-4 w-4\" />\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 地址栏 */}\n      {selectedClient && (\n        <div className=\"bg-white border-b border-gray-200 px-4 py-2\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex items-center space-x-1 text-sm\">\n              {pathSegments.map((segment, index) => (\n                <div key={index} className=\"flex items-center\">\n                  {index > 0 && <ChevronRight className=\"h-3 w-3 text-gray-400 mx-1\" />}\n                  <button\n                    onClick={() => {\n                      const newPath = pathSegments.slice(0, index + 1).join('/')\n                      navigateToPath(newPath.includes(':') ? newPath : 'C:/' + newPath)\n                    }}\n                    className=\"px-2 py-1 text-gray-700 hover:bg-gray-100 rounded\"\n                  >\n                    {segment}\n                  </button>\n                </div>\n              ))}\n            </div>\n            <div className=\"flex-1\" />\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"搜索文件...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10 pr-4 py-1.5 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 w-64\"\n              />\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 主要内容区域 */}\n      <div className=\"flex-1 flex overflow-hidden\">\n        {selectedClient ? (\n          <>\n            {/* 左侧树形目录 */}\n            <div className=\"w-64 bg-white border-r border-gray-200 overflow-y-auto\">\n              <div className=\"p-2\">\n                <div className=\"text-xs font-medium text-gray-500 uppercase tracking-wider mb-2 px-2\">\n                  此电脑\n                </div>\n                {treeData.map(node => renderTreeNode(node))}\n              </div>\n            </div>\n\n            {/* 右侧文件列表 */}\n            <div className=\"flex-1 flex flex-col overflow-hidden\">\n              {/* 文件列表头部 */}\n              <div className=\"bg-gray-50 border-b border-gray-200 px-4 py-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-gray-600\">\n                    {filteredFiles.length} 个项目\n                  </span>\n                  {selectedFiles.length > 0 && (\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-blue-600\">已选择 {selectedFiles.length} 个项目</span>\n                      <button className=\"px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700\">\n                        删除\n                      </button>\n                      <button className=\"px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700\">\n                        下载\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* 文件列表内容 */}\n              <div className=\"flex-1 overflow-auto\">\n                {viewMode === 'list' ? (\n                  /* 列表视图 */\n                  <table className=\"w-full\">\n                    <thead className=\"bg-gray-50 sticky top-0 border-b border-gray-200\">\n                      <tr>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8\">\n                          <input\n                            type=\"checkbox\"\n                            className=\"rounded border-gray-300\"\n                            onChange={(e) => {\n                              if (e.target.checked) {\n                                setSelectedFiles(filteredFiles.map(f => f.id))\n                              } else {\n                                setSelectedFiles([])\n                              }\n                            }}\n                          />\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          名称\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24\">\n                          大小\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40\">\n                          修改时间\n                        </th>\n                        <th className=\"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32\">\n                          操作\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-100\">\n                      {filteredFiles.map((file) => (\n                        <tr\n                          key={file.id}\n                          className={`hover:bg-gray-50 cursor-pointer ${\n                            selectedFiles.includes(file.id) ? 'bg-blue-50' : ''\n                          }`}\n                          onDoubleClick={() => {\n                            if (file.type === 'folder') {\n                              navigateToPath(file.path)\n                            }\n                          }}\n                        >\n                          <td className=\"px-4 py-2\">\n                            <input\n                              type=\"checkbox\"\n                              className=\"rounded border-gray-300\"\n                              checked={selectedFiles.includes(file.id)}\n                              onChange={(e) => {\n                                e.stopPropagation()\n                                if (e.target.checked) {\n                                  setSelectedFiles([...selectedFiles, file.id])\n                                } else {\n                                  setSelectedFiles(selectedFiles.filter(id => id !== file.id))\n                                }\n                              }}\n                            />\n                          </td>\n                          <td className=\"px-4 py-2\">\n                            <div className=\"flex items-center\">\n                              {getFileIcon(file)}\n                              <span className=\"ml-2 text-sm text-gray-900 truncate\">\n                                {file.name}\n                              </span>\n                            </div>\n                          </td>\n                          <td className=\"px-4 py-2 text-sm text-gray-500\">\n                            {file.type === 'file' && file.size ? formatFileSize(file.size) : ''}\n                          </td>\n                          <td className=\"px-4 py-2 text-sm text-gray-500\">\n                            {file.modified}\n                          </td>\n                          <td className=\"px-4 py-2\">\n                            <div className=\"flex items-center space-x-1\">\n                              <button className=\"p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded\">\n                                <Download className=\"h-3 w-3\" />\n                              </button>\n                              <button className=\"p-1 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded\">\n                                <Edit className=\"h-3 w-3\" />\n                              </button>\n                              <button className=\"p-1 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded\">\n                                <Copy className=\"h-3 w-3\" />\n                              </button>\n                              <button className=\"p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded\">\n                                <Trash2 className=\"h-3 w-3\" />\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                ) : (\n                  /* 网格视图 */\n                  <div className=\"p-4 grid grid-cols-6 gap-4\">\n                    {filteredFiles.map((file) => (\n                      <div\n                        key={file.id}\n                        className={`p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer text-center ${\n                          selectedFiles.includes(file.id) ? 'bg-blue-50 border-blue-300' : ''\n                        }`}\n                        onDoubleClick={() => {\n                          if (file.type === 'folder') {\n                            navigateToPath(file.path)\n                          }\n                        }}\n                        onClick={() => {\n                          if (selectedFiles.includes(file.id)) {\n                            setSelectedFiles(selectedFiles.filter(id => id !== file.id))\n                          } else {\n                            setSelectedFiles([...selectedFiles, file.id])\n                          }\n                        }}\n                      >\n                        <div className=\"flex justify-center mb-2\">\n                          {React.cloneElement(getFileIcon(file), { className: 'h-8 w-8' })}\n                        </div>\n                        <div className=\"text-xs text-gray-900 truncate\" title={file.name}>\n                          {file.name}\n                        </div>\n                        {file.type === 'file' && file.size && (\n                          <div className=\"text-xs text-gray-500 mt-1\">\n                            {formatFileSize(file.size)}\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                )}\n\n                {/* 空状态 */}\n                {filteredFiles.length === 0 && (\n                  <div className=\"flex-1 flex items-center justify-center\">\n                    <div className=\"text-center\">\n                      <Folder className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                      <h3 className=\"text-sm font-medium text-gray-900 mb-2\">此文件夹为空</h3>\n                      <p className=\"text-sm text-gray-500\">\n                        {searchTerm ? '没有找到匹配的文件' : '此文件夹中没有任何文件或文件夹'}\n                      </p>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </>\n        ) : (\n          <div className=\"flex-1 flex items-center justify-center\">\n            <div className=\"text-center\">\n              <FolderOpen className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">选择客户端</h3>\n              <p className=\"text-gray-500\">请先选择一个客户端来管理文件</p>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+BA;AAlCA;;;;;AAwEA,SAAS;AACT,MAAM,eAA2B;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;YACR;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,UAAU;oBACR;wBACE,IAAI;wBACJ,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,UAAU;wBACV,UAAU;4BACR;gCACE,IAAI;gCACJ,MAAM;gCACN,MAAM;gCACN,MAAM;4BACR;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,MAAM;gCACN,MAAM;4BACR;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,MAAM;gCACN,MAAM;4BACR;4BACA;gCACE,IAAI;gCACJ,MAAM;gCACN,MAAM;gCACN,MAAM;4BACR;yBACD;oBACH;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD;IACH;CACD;AAED,kBAAkB;AAClB,MAAM,kBAAkB,CAAC;IACvB,MAAM,UAAyC;QAC7C,0BAA0B;YACxB;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,MAAM;YACR;SACD;QACD,oCAAoC;YAClC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,MAAM;YACR;SACD;QACD,oCAAoC;YAClC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,MAAM;YACR;SACD;QACD,mCAAmC;YACjC;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,WAAW;gBACX,MAAM;YACR;SACD;IACH;IAEA,OAAO,OAAO,CAAC,KAAK,IAAI,EAAE;AAC5B;AAEe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAyB;IAC/F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,MAAM,aAAa,kBAAkB,KAAK,CAAC,GAAG,eAAe;QAC7D,WAAW,IAAI,CAAC;QAChB,qBAAqB;QACrB,gBAAgB,WAAW,MAAM,GAAG;IACtC;IAEA,MAAM,SAAS;QACb,IAAI,eAAe,GAAG;YACpB,gBAAgB,eAAe;YAC/B,eAAe,iBAAiB,CAAC,eAAe,EAAE;QACpD;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,eAAe,kBAAkB,MAAM,GAAG,GAAG;YAC/C,gBAAgB,eAAe;YAC/B,eAAe,iBAAiB,CAAC,eAAe,EAAE;QACpD;IACF;IAEA,MAAM,OAAO;QACX,MAAM,YAAY,YAAY,KAAK,CAAC,KAAK,MAAM,CAAC;QAChD,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,MAAM,aAAa,UAAU,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YAC/C,eAAe,WAAW,UAAU,CAAC,SAAS,WAAW,UAAU,CAAC,QAAQ,aAAa,OAAO;QAClG;IACF;IAEA,WAAW;IACX,MAAM,iBAAiB,CAAC;QACtB,MAAM,aAAa,CAAC;YAClB,OAAO,MAAM,GAAG,CAAC,CAAA;gBACf,IAAI,KAAK,EAAE,KAAK,QAAQ;oBACtB,OAAO;wBAAE,GAAG,IAAI;wBAAE,UAAU,CAAC,KAAK,QAAQ;oBAAC;gBAC7C;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,OAAO;wBAAE,GAAG,IAAI;wBAAE,UAAU,WAAW,KAAK,QAAQ;oBAAE;gBACxD;gBACA,OAAO;YACT;QACF;QACA,YAAY,WAAW;IACzB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,IAAI,KAAK,UAAU;YAC1B,qBAAO,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC3B;QAEA,MAAM,MAAM,KAAK,SAAS,EAAE;QAC5B,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,IAAI,KAAK,SAAS;YACzB,qBAAO,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC9B;QACA,qBAAO,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IAEA,cAAc;IACd,MAAM,eAAe,gBAAgB;IACrC,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,OACxC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGzD,SAAS;IACT,MAAM,eAAe,YAAY,KAAK,CAAC,UAAU,MAAM,CAAC;IAExD,SAAS;IACT,MAAM,iBAAiB,CAAC,MAAgB,QAAQ,CAAC,iBAC/C,8OAAC;;8BACC,8OAAC;oBACC,WAAW,CAAC,qEAAqE,EAC/E,gBAAgB,KAAK,IAAI,GAAG,6BAA6B,iBACzD;oBACF,OAAO;wBAAE,aAAa,GAAG,QAAQ,KAAK,EAAE,EAAE,CAAC;oBAAC;oBAC5C,SAAS;wBACP,IAAI,KAAK,IAAI,KAAK,UAAU;4BAC1B,eAAe,KAAK,IAAI;wBAC1B,OAAO;4BACL,eAAe,KAAK,IAAI;wBAC1B;oBACF;;wBAEC,KAAK,QAAQ,kBACZ,8OAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB,eAAe,KAAK,EAAE;4BACxB;4BACA,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCACX,WAAW,CAAC,6BAA6B,EACvC,KAAK,QAAQ,GAAG,cAAc,IAC9B;;;;;;;;;;;wBAIP,CAAC,KAAK,QAAQ,kBAAI,8OAAC;4BAAI,WAAU;;;;;;wBACjC,YAAY;sCACb,8OAAC;4BAAK,WAAU;sCAAiB,KAAK,IAAI;;;;;;;;;;;;gBAE3C,KAAK,QAAQ,IAAI,KAAK,QAAQ,kBAC7B,8OAAC;8BACE,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,QAAS,eAAe,OAAO,QAAQ;;;;;;;WAnCtD,KAAK,EAAE;;;;;IAyCnB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;;;;;0BAKxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,UAAc;oCACb,gBAAgB;oCAChB,gBAAgB;;;;;;gCAEjB,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;;gDAAK;gDAAO,eAAe,EAAE,CAAC,QAAQ,CAAC,aAAa,SAAS;;;;;;;;;;;;;;;;;;;sCAMpE,8OAAC;4BAAI,WAAU;sCACZ,gCACC;;kDACE,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGrC,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YAUlD,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU,gBAAgB;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,8OAAC;oCACC,SAAS;oCACT,UAAU,gBAAgB,kBAAkB,MAAM,GAAG;oCACrD,WAAU;8CAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAW,CAAC,cAAc,EAAE,aAAa,SAAS,8BAA8B,mCAAmC;8CAEnH,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCACC,SAAS,IAAM,YAAY;oCAC3B,WAAW,CAAC,cAAc,EAAE,aAAa,SAAS,8BAA8B,mCAAmC;8CAEnH,cAAA,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,8NAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQnC,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;oCAAgB,WAAU;;wCACxB,QAAQ,mBAAK,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACtC,8OAAC;4CACC,SAAS;gDACP,MAAM,UAAU,aAAa,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;gDACtD,eAAe,QAAQ,QAAQ,CAAC,OAAO,UAAU,QAAQ;4CAC3D;4CACA,WAAU;sDAET;;;;;;;mCATK;;;;;;;;;;sCAcd,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,8OAAC;gBAAI,WAAU;0BACZ,+BACC;;sCAEE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAuE;;;;;;oCAGrF,SAAS,GAAG,CAAC,CAAA,OAAQ,eAAe;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDACb,cAAc,MAAM;oDAAC;;;;;;;4CAEvB,cAAc,MAAM,GAAG,mBACtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DAAgB;4DAAK,cAAc,MAAM;4DAAC;;;;;;;kEAC1D,8OAAC;wDAAO,WAAU;kEAAmE;;;;;;kEAGrF,8OAAC;wDAAO,WAAU;kEAAqE;;;;;;;;;;;;;;;;;;;;;;;8CAS/F,8OAAC;oCAAI,WAAU;;wCACZ,aAAa,SACZ,QAAQ,iBACR,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDAAM,WAAU;8DACf,cAAA,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEACC,MAAK;oEACL,WAAU;oEACV,UAAU,CAAC;wEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;4EACpB,iBAAiB,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;wEAC9C,OAAO;4EACL,iBAAiB,EAAE;wEACrB;oEACF;;;;;;;;;;;0EAGJ,8OAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,8OAAC;gEAAG,WAAU;0EAAsF;;;;;;0EAGpG,8OAAC;gEAAG,WAAU;0EAAsF;;;;;;0EAGpG,8OAAC;gEAAG,WAAU;0EAAsF;;;;;;;;;;;;;;;;;8DAKxG,8OAAC;oDAAM,WAAU;8DACd,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;4DAEC,WAAW,CAAC,gCAAgC,EAC1C,cAAc,QAAQ,CAAC,KAAK,EAAE,IAAI,eAAe,IACjD;4DACF,eAAe;gEACb,IAAI,KAAK,IAAI,KAAK,UAAU;oEAC1B,eAAe,KAAK,IAAI;gEAC1B;4DACF;;8EAEA,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEACC,MAAK;wEACL,WAAU;wEACV,SAAS,cAAc,QAAQ,CAAC,KAAK,EAAE;wEACvC,UAAU,CAAC;4EACT,EAAE,eAAe;4EACjB,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;gFACpB,iBAAiB;uFAAI;oFAAe,KAAK,EAAE;iFAAC;4EAC9C,OAAO;gFACL,iBAAiB,cAAc,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,EAAE;4EAC5D;wEACF;;;;;;;;;;;8EAGJ,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;;4EACZ,YAAY;0FACb,8OAAC;gFAAK,WAAU;0FACb,KAAK,IAAI;;;;;;;;;;;;;;;;;8EAIhB,8OAAC;oEAAG,WAAU;8EACX,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,GAAG,eAAe,KAAK,IAAI,IAAI;;;;;;8EAEnE,8OAAC;oEAAG,WAAU;8EACX,KAAK,QAAQ;;;;;;8EAEhB,8OAAC;oEAAG,WAAU;8EACZ,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAO,WAAU;0FAChB,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;;;;;;0FAEtB,8OAAC;gFAAO,WAAU;0FAChB,cAAA,8OAAC,2MAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;0FAElB,8OAAC;gFAAO,WAAU;0FAChB,cAAA,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;0FAElB,8OAAC;gFAAO,WAAU;0FAChB,cAAA,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2DAnDnB,KAAK,EAAE;;;;;;;;;;;;;;;mDA4DpB,QAAQ,iBACR,8OAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oDAEC,WAAW,CAAC,kFAAkF,EAC5F,cAAc,QAAQ,CAAC,KAAK,EAAE,IAAI,+BAA+B,IACjE;oDACF,eAAe;wDACb,IAAI,KAAK,IAAI,KAAK,UAAU;4DAC1B,eAAe,KAAK,IAAI;wDAC1B;oDACF;oDACA,SAAS;wDACP,IAAI,cAAc,QAAQ,CAAC,KAAK,EAAE,GAAG;4DACnC,iBAAiB,cAAc,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,EAAE;wDAC5D,OAAO;4DACL,iBAAiB;mEAAI;gEAAe,KAAK,EAAE;6DAAC;wDAC9C;oDACF;;sEAEA,8OAAC;4DAAI,WAAU;sEACZ,cAAA,qMAAA,CAAA,UAAK,CAAC,YAAY,CAAC,YAAY,OAAO;gEAAE,WAAW;4DAAU;;;;;;sEAEhE,8OAAC;4DAAI,WAAU;4DAAiC,OAAO,KAAK,IAAI;sEAC7D,KAAK,IAAI;;;;;;wDAEX,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,kBAChC,8OAAC;4DAAI,WAAU;sEACZ,eAAe,KAAK,IAAI;;;;;;;mDAzBxB,KAAK,EAAE;;;;;;;;;;wCAkCnB,cAAc,MAAM,KAAK,mBACxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEACV,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAS1C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}
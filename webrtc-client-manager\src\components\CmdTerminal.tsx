'use client'

import { useState, useRef, useEffect } from 'react'
import { 
  Terminal, 
  Send, 
  Copy, 
  Download, 
  Settings, 
  Trash2,
  CheckCircle,
  XCircle,
  Square,
  WifiOff
} from 'lucide-react'
import { Client, CommandHistory } from '@/types/client'

interface CmdTerminalProps {
  client: Client | null
}

export default function CmdTerminal({ client }: CmdTerminalProps) {
  const [currentCommand, setCurrentCommand] = useState('')
  const [isRunning, setIsRunning] = useState(false)
  const [commandHistory, setCommandHistory] = useState<CommandHistory[]>([])
  
  const terminalRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // 当客户端改变时，清空历史记录
  useEffect(() => {
    if (client) {
      setCommandHistory([])
      setCurrentCommand('')
      setIsRunning(false)
      
      // 添加欢迎信息
      const welcomeCommand: CommandHistory = {
        id: Date.now().toString(),
        command: '',
        output: `Microsoft Windows [版本 10.0.22621.2715]
(c) Microsoft Corporation. 保留所有权利。

已连接到 ${client.name} (${client.ip})
当前用户: ${client.owner.name}
系统: ${client.os}`,
        status: 'success',
        timestamp: new Date().toLocaleTimeString()
      }
      
      setCommandHistory([welcomeCommand])
    }
  }, [client])

  useEffect(() => {
    // 自动滚动到底部
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight
    }
  }, [commandHistory])

  const executeCommand = () => {
    if (!currentCommand.trim() || isRunning || !client) return

    const newCommand: CommandHistory = {
      id: Date.now().toString(),
      command: currentCommand,
      output: '',
      status: 'running',
      timestamp: new Date().toLocaleTimeString()
    }

    setCommandHistory(prev => [...prev, newCommand])
    setIsRunning(true)
    setCurrentCommand('')

    // 模拟命令执行
    setTimeout(() => {
      const mockOutput = generateMockOutput(newCommand.command)
      const isSuccess = !newCommand.command.toLowerCase().includes('error')
      
      setCommandHistory(prev => 
        prev.map(cmd => 
          cmd.id === newCommand.id 
            ? {
                ...cmd,
                output: mockOutput,
                status: isSuccess ? 'success' : 'error',
                duration: Math.random() * 2 + 0.5
              }
            : cmd
        )
      )
      setIsRunning(false)
      
      // 重新聚焦输入框
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }, 1000 + Math.random() * 2000)
  }

  const generateMockOutput = (command: string): string => {
    const cmd = command.toLowerCase().trim()
    
    if (cmd === 'dir') {
      return `驱动器 C 中的卷没有标签。
卷的序列号是 1234-5678

C:\\Users\\<USER>\\Windows
系统目录:         C:\\Windows\\system32
启动设备:         \\Device\\HarddiskVolume2
系统区域设置:     zh-cn;中文(中国)
输入法区域设置:   zh-cn;中文(中国)
时区:             (UTC+08:00) 北京，重庆，香港特别行政区，乌鲁木齐
物理内存总量:     ${client?.memory.total ? Math.round(client.memory.total / 1024 / 1024) : 16384} MB
可用的物理内存:   ${client?.memory.available ? Math.round(client.memory.available / 1024 / 1024) : 8192} MB
虚拟内存: 最大值: 32,768 MB
虚拟内存: 可用:   16,384 MB
虚拟内存: 使用中: 16,384 MB`
    }
    
    if (cmd === 'ipconfig') {
      return `Windows IP 配置

以太网适配器 以太网:

   连接特定的 DNS 后缀 . . . . . . . : 
   本地链接 IPv6 地址. . . . . . . . : fe80::1234:5678:9abc:def0%12
   IPv4 地址 . . . . . . . . . . . . : ${client?.ip || '*************'}
   子网掩码  . . . . . . . . . . . . : *************
   默认网关. . . . . . . . . . . . . : ***********`
    }
    
    if (cmd.startsWith('ping')) {
      const target = cmd.split(' ')[1] || 'google.com'
      return `正在 Ping ${target} [**************] 具有 32 字节的数据:
来自 ************** 的回复: 字节=32 时间=15ms TTL=117
来自 ************** 的回复: 字节=32 时间=14ms TTL=117
来自 ************** 的回复: 字节=32 时间=16ms TTL=117
来自 ************** 的回复: 字节=32 时间=15ms TTL=117

${target} 的 Ping 统计信息:
    数据包: 已发送 = 4，已接收 = 4，丢失 = 0 (0% 丢失)，
往返行程的估计时间(以毫秒为单位):
    最短 = 14ms，最长 = 16ms，平均 = 15ms`
    }
    
    if (cmd === 'tasklist') {
      return `映像名称                       PID 会话名              会话#       内存使用
========================= ======== ================ =========== ============
System Idle Process              0 Services                   0          8 K
System                           4 Services                   0      1,024 K
smss.exe                       348 Services                   0      1,048 K
csrss.exe                      424 Services                   0      4,096 K
wininit.exe                    500 Services                   0      2,048 K
csrss.exe                      508 Console                    1      8,192 K
winlogon.exe                   564 Console                    1      4,096 K
services.exe                   612 Services                   0      8,192 K
lsass.exe                      620 Services                   0     16,384 K
svchost.exe                    728 Services                   0     32,768 K`
    }
    
    if (cmd.startsWith('netstat')) {
      return `活动连接

  协议  本地地址          外部地址        状态
  TCP    0.0.0.0:135           0.0.0.0:0              LISTENING
  TCP    0.0.0.0:445           0.0.0.0:0              LISTENING
  TCP    0.0.0.0:3389          0.0.0.0:0              LISTENING
  TCP    0.0.0.0:5040          0.0.0.0:0              LISTENING
  TCP    127.0.0.1:1433        0.0.0.0:0              LISTENING
  TCP    ${client?.ip || '*************'}:139     0.0.0.0:0              LISTENING
  TCP    ${client?.ip || '*************'}:3389    ************:52341     ESTABLISHED
  TCP    [::]:135              [::]:0                 LISTENING
  TCP    [::]:445              [::]:0                 LISTENING
  TCP    [::]:3389             [::]:0                 LISTENING`
    }
    
    if (cmd.includes('error')) {
      return `'${command}' 不是内部或外部命令，也不是可运行的程序或批处理文件。`
    }
    
    return `命令 "${command}" 已执行完成。`
  }

  const clearHistory = () => {
    setCommandHistory([])
  }

  const copyOutput = (output: string) => {
    navigator.clipboard.writeText(output)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      executeCommand()
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-3 w-3 text-green-400" />
      case 'error':
        return <XCircle className="h-3 w-3 text-red-400" />
      case 'running':
        return <div className="h-3 w-3 border border-blue-400 border-t-transparent rounded-full animate-spin" />
      default:
        return null
    }
  }

  if (!client) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Terminal className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">CMD终端</h3>
          <p className="text-gray-500">请从左侧选择一个客户端开始命令行操作</p>
        </div>
      </div>
    )
  }

  if (client.status === 'offline') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <WifiOff className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">客户端离线</h3>
          <p className="text-gray-500">客户端 {client.name} 当前离线，无法执行命令</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-50">
      {/* 控制栏 */}
      <div className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-sm font-medium text-gray-900">
              CMD终端 - {client.name}
            </h3>
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <span>终端类型: {client.os.includes('Windows') ? 'CMD' : 'Bash'}</span>
              <span>会话时间: 00:15:32</span>
              <span>命令数: {commandHistory.filter(cmd => cmd.command).length}</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={clearHistory}
              className="px-3 py-1.5 bg-red-600 text-white rounded text-sm hover:bg-red-700 flex items-center"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              清空
            </button>
            <button className="px-3 py-1.5 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 flex items-center">
              <Download className="h-4 w-4 mr-1" />
              导出
            </button>
            <button className="px-3 py-1.5 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 flex items-center">
              <Settings className="h-4 w-4 mr-1" />
              设置
            </button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 终端输出区域 */}
        <div 
          ref={terminalRef}
          className="flex-1 bg-black text-green-400 font-mono text-sm p-4 overflow-y-auto"
        >
          {commandHistory.map((cmd) => (
            <div key={cmd.id} className="mb-4">
              {/* 命令行 */}
              {cmd.command && (
                <div className="flex items-center mb-1">
                  <span className="text-cyan-400">C:\\Users\\<USER>\\Users\\Administrator&gt;</span>
            <input
              ref={inputRef}
              type="text"
              value={currentCommand}
              onChange={(e) => setCurrentCommand(e.target.value)}
              onKeyPress={handleKeyPress}
              className="ml-2 bg-transparent text-white outline-none flex-1 font-mono"
              placeholder={isRunning ? "正在执行命令..." : "输入命令..."}
              disabled={isRunning}
              autoFocus
            />
          </div>
        </div>

        {/* 命令输入区域 */}
        <div className="bg-gray-800 border-t border-gray-600 p-4">
          <div className="flex items-center space-x-2">
            <div className="flex-1 flex items-center bg-gray-700 rounded-md">
              <Terminal className="h-5 w-5 text-gray-400 ml-3" />
              <input
                type="text"
                value={currentCommand}
                onChange={(e) => setCurrentCommand(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入CMD命令..."
                className="flex-1 bg-transparent text-white px-3 py-2 outline-none"
                disabled={isRunning}
              />
            </div>
            <button
              onClick={executeCommand}
              disabled={!currentCommand.trim() || isRunning}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isRunning ? (
                <>
                  <Square className="h-4 w-4 mr-2" />
                  停止
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  执行
                </>
              )}
            </button>
          </div>

          {/* 快捷命令 */}
          <div className="mt-3 flex flex-wrap gap-2">
            {['dir', 'systeminfo', 'ipconfig', 'ping google.com', 'tasklist', 'netstat -an'].map((cmd) => (
              <button
                key={cmd}
                onClick={() => setCurrentCommand(cmd)}
                className="px-3 py-1 bg-gray-600 text-gray-300 text-sm rounded hover:bg-gray-500"
                disabled={isRunning}
              >
                {cmd}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
